﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_MOVE_OUT.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_PLAN_DOWN : AvalonDock.DocumentContent
    {
        string _STOCK_BARCODE = string.Empty;

        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        List<Model.MANAGE_LIST> listMANAGE_LIST = null;

        int planId = 0;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_PLAN_DOWN(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp.I_DatabaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                ((QueryWhere) =>
                {
                    QueryWhere = string.Format("{0} AND {1}", this.ucSplitPanel.U_GetSplitPropertyWhere(), string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere);
                    this.StorageListBind(QueryWhere);
                }
                );
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_PLAN_DOWN(int PLAN_ID, string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp.I_DatabaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

            planId = PLAN_ID;
            string sPLAN = $" and ((SELECT count(0) FROM PLAN_LIST WHERE V_STORAGE_LIST.GOODS_ID= PLAN_LIST.GOODS_ID AND  ISNULL(V_STORAGE_LIST.GOODS_PROPERTY1,'')= ISNULL(PLAN_LIST.GOODS_PROPERTY1,'')  AND  PLAN_ID = '{planId}')>0)";

            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                ((QueryWhere) =>
                {
                    QueryWhere = string.Format("{0} AND {1} {2}",
                        this.ucSplitPanel.U_GetSplitPropertyWhere(),
                        string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere,
                        1 == 2 ? string.Empty : sPLAN);
                    this.StorageListBind(QueryWhere);
                }
                );
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_PLAN_DOWN(string MANAGE_TYPE_CODE, string STOCK_BARCODE)
            : this(MANAGE_TYPE_CODE)
        {
            this._STOCK_BARCODE = STOCK_BARCODE;
        }


        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //输送位置
            this.InitManagePosotion();
            //属性控件
            this.InitSplitPropertyPanel();
            //查询控件
            this.InitQueryControl();

            if (!string.IsNullOrEmpty(this._STOCK_BARCODE))
            {
                this.ucQuery.U_ButtonClick(this._STOCK_BARCODE);
            }

            //fst add 2018-12-01
            //if (mMANAGE_TYPE.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageDownForce.ToString())
            //{
            //    this.cbxCheckCellStatus.Visibility = Visibility.Visible;
            //    this.cbxDownloadControl.Visibility = Visibility.Visible;
            //}           
        }

        /// <summary>
        /// 初始化输送位置控件
        /// </summary>
        private void InitManagePosotion()
        {
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
        }

        /// <summary>
        /// fst add 2018-12-01 是否校验起止货位可用状态
        /// </summary>
        private void cbxCheckCellStatus_CheckChanged(object sender, RoutedEventArgs e)
        {
            this.ucManagePosition.U_IsCheckCellEnable = this.cbxCheckCellStatus.IsChecked == null ? true : (bool)this.cbxCheckCellStatus.IsChecked;

            if (mMANAGE_TYPE != null)
            {
                this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
            }
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            this.ucQuery.U_WindowName = this.GetType().Name;
            this.ucQuery.U_XmlTableName = "V_STORAGE_LIST";
            this.ucQuery.U_InitControl();
        }

        /// <summary>
        /// 初始化属性面板
        /// </summary>
        private void InitSplitPropertyPanel()
        {
            this.ucSplitPanel.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucSplitPanel.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            this.ucSplitPanel.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.ucSplitPanel.U_SplitPropertyType = "GOODS_TYPE";
            this.ucSplitPanel.U_InitControl();
        }

        private void StorageListBind(string QueryWhere)
        {
            //plan_lists
            string patch = string.Empty;
            string sql = $"select plan_list_code from plan_list where plan_id = {planId}";
            DataTable lists = MainApp.I_DatabaseService.GetList(sql);
            if (lists != null && lists.Rows.Count > 0)
            {
                foreach (DataRow item in lists.Rows)
                {
                    patch += $" '{item["plan_list_code"].ToString()}',";
                }
                QueryWhere += $" and cell_code in ({patch.TrimEnd(',')})";
            }



            this.ucStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            //this.ucStorageGroup.U_AppendFieldStyles = this.GetColumnDescriptionList();
            this.ucStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStorageGroup.U_OrderField = "STORAGE_LIST_ID";
            this.ucStorageGroup.U_Where = string.Format(" cell_status <> 'Pallet1'  AND AREA_TYPE= 'LiKu' and cell_id not in ( select start_cell_id from manage_main) and {0} ",
                                                            QueryWhere);

            this.ucStorageGroup.U_AllowOperatData = false;
            this.ucStorageGroup.U_AllowChecked = true;
            this.ucStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStorageGroup.U_InitControl();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = false;

            bool boolSingleResult = true;

            string strResult = "\n";

            string sSingleReslut = string.Empty;

            try
            {
                //tzyg alter 2017-04-13 start
                ////校验填写仓库信息是否合法
                //if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                //    return;

                ////获得选中记录
                //List<DataRowView> listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();

                //foreach (DataRowView drv in listDataRowView)
                //{
                //    drv["MANAGE_LIST_QUANTITY"] = drv["STORAGE_LIST_QUANTITY"];
                //}

                ////校验是否选中记录
                //if (listDataRowView.Count == 0)
                //{
                //    MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                //    return;
                //}

                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                //获得选中记录
                List<DataRowView> listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();

                //校验是否选中记录
                if (listDataRowView.Count != 1)
                {
                    MainApp._MessageDialog.ShowResult(false, "只允许选择1条信息");
                    return;
                }

                //xcjt add 2017-04-12  
                //下架页面选中一条数据后将同托盘条码的条目都自动选中
                var checkedBarcode = (from r in listDataRowView select r["STOCK_BARCODE"]).ToList();
                foreach (DataRowView drv in listDataRowView[0].DataView)
                {
                    if (checkedBarcode.Contains(drv["STOCK_BARCODE"].ToString()))
                    {
                        this.ucStorageGroup.U_CheckRow(drv);
                    }
                }
                //重新获得选中记录
                listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();


                foreach (DataRowView drv in listDataRowView)
                {
                    drv["MANAGE_LIST_QUANTITY"] = drv["STORAGE_LIST_QUANTITY"];
                }


                //tzyg alter 2017-04-13 end


                var cell_id_group = from v in listDataRowView
                                    group v by v["CELL_ID"].ToString() into a
                                    select a;


                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {

                    foreach (var cell_id in cell_id_group)
                    {
                        int intStartPositionID = Convert.ToInt32(cell_id.Key);

                        var value_ceLl_id = from v in listDataRowView
                                            where v["CELL_ID"].ToString() == cell_id.Key
                                            select v;

                        DataRowView[] drvGroup = value_ceLl_id.Cast<DataRowView>().ToArray();

                        listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(drvGroup, null);

                        Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.CELL_MODEL = drvGroup[0]["CELL_MODEL"].ToString();
                        mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                        mMANAGE_MAIN.MANAGE_LEVEL = MainApp.I_SystemService.GetSysParameter("ManualOutLevel", "0");
                        mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                        mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                        mMANAGE_MAIN.MANAGE_SOURCE = Enum.SystemName.iWMS.ToString();
                        mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.Waiting.ToString();
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                        mMANAGE_MAIN.PLAN_ID = planId;
                        mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                        mMANAGE_MAIN.START_CELL_ID = intStartPositionID;
                        mMANAGE_MAIN.STOCK_BARCODE = drvGroup[0]["STOCK_BARCODE"].ToString();
                        mMANAGE_MAIN.STOCK_WEIGHT = int.TryParse(drvGroup[0]["STOCK_WEIGHT"].ToString(), out int stockWeight) ? stockWeight : 0;

                        boolSingleResult = MainApp.I_ManageService.ManageCreate(
                            mMANAGE_MAIN,
                            null,
                            raiseTrans: true,
                            checkStorage: true,
                            checkManage: true,
                            checkCellStatus: this.ucManagePosition.U_IsCheckCellEnable,
                            autoComplete: this.cbxDownloadControl.IsChecked == null ? this.ucManagePosition.U_AutoCompleteTask : !(bool)this.cbxDownloadControl.IsChecked,
                            autoControl: this.cbxDownloadControl.IsChecked == null ? this.ucManagePosition.U_AutoDownloadControlTask : (bool)this.cbxDownloadControl.IsChecked,
                            doubleInAutoMove: !true,
                            out sSingleReslut);

                        if (!boolSingleResult)
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达失败 " + sSingleReslut + "\n";

                            //break;
                        }
                        else
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达成功 " + "\n";

                            boolResult = true;

                            //break;
                        }
                    }

                    this.Refresh();
                }


                MainApp._MessageDialog.ShowResult(boolResult, strResult);

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 获得托盘集合列表
        /// </summary>
        private IDictionary<string, Model.WH_CELL> GetPalletKeyValuePair(List<DataRowView> listDataRowView)
        {
            IDictionary<string, Model.WH_CELL> dicStack = new Dictionary<string, Model.WH_CELL>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack = rowView["STOCK_BARCODE"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp.I_DatabaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (mWH_CELL != null && !dicStack.ContainsKey(stack))
                    {
                        dicStack.Add(stack, mWH_CELL);
                    }
                }
            }
            return dicStack;
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            //刷新
            this.ucManagePosition.U_Update();

            this.ucStorageGroup.U_InitControl();
        }


    }
}
