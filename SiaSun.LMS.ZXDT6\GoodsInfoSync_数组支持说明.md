# GoodsInfoSync 数组支持功能说明

## 概述

已成功修改 `SiaSun.LMS.Implement.Interface.WMS.GoodsInfoSync.cs` 文件，使其能够接收和处理数组格式的输入参数，同时保持对原有单个对象格式的向后兼容性。**保持了原有的 OutputParam 类结构不变**，并完善了所有异常处理逻辑。

## 主要修改内容

### 1. 输入格式支持

**直接数组格式**：代码现在支持直接接收物料对象的数组格式，无需额外的包装字段。

### 2. 保持原有输出参数结构

**OutputParam 类保持原始结构不变**：
```csharp
class OutputParam
{
    public int code { get; set; }
    public string msg { get; set; }
    public string traceId { get; set; }
}
```

### 2. 重构主要接口方法

#### 智能输入解析
- **首先尝试解析为直接数组格式** (`List<InputParam>`)
- 如果失败，则尝试解析为单个对象格式 (`InputParam`) - 保持向后兼容
- 如果两种格式都解析失败，返回错误信息
- **完善了所有 try-catch 块的异常处理逻辑**
- **修复了 ILog.Warning 方法调用问题**，改为 ILog.Warn

#### 批量处理逻辑
- 循环处理每个物料信息
- 统计成功和失败的数量
- 收集错误消息并在返回消息中包含详细错误信息
- 根据处理结果设置整体返回状态

### 4. 重构 ProcessSingleGoods 方法

将原有的单个物料处理逻辑提取为独立方法：
- **返回类型改为 string**：成功时返回空字符串，失败时返回错误消息
- 参数验证（物料编码、名称、状态等）
- 数据库操作（创建或更新物料信息）
- 处理嵌套的单位信息和品牌信息
- **完善的异常处理和详细日志记录**

### 5. 完善异常处理

#### 主要改进：
- **所有 try-catch 块都有完整的异常处理逻辑**
- **详细的日志记录**，包含异常消息和堆栈信息
- **参数验证**，防止空引用异常
- **数据库操作异常处理**，分别处理查询、创建、更新异常
- **序列化异常处理**，确保输出格式的稳定性
- **嵌套处理异常**，单位信息和品牌信息处理失败不影响主流程

## 输入格式支持

### 单个物料格式（向后兼容）
```json
{
    "goodsCode": "TEST001",
    "name": "测试物料001",
    "goodsStatus": 1,
    "unitName": "个",
    "goodsVersion": "V1.0",
    "brandVOs": [
        {
            "brandName": "测试品牌",
            "purchasesNum": "1",
            "inboundQuantity": 100
        }
    ]
}
```

### 直接数组格式（新增）
```json
[
    {
        "goodsCode": "BATCH001",
        "name": "批量测试物料001",
        "goodsStatus": 1,
        "unitName": "个",
        "goodsVersion": "V1.0",
        "brandVOs": [
            {
                "brandName": "品牌A",
                "purchasesNum": "1",
                "inboundQuantity": 50
            }
        ]
    },
    {
        "goodsCode": "BATCH002",
        "name": "批量测试物料002",
        "goodsStatus": 0,
        "unitName": "套",
        "goodsVersion": "V2.0",
        "brandVOs": [
            {
                "brandName": "品牌B",
                "purchasesNum": "2",
                "inboundQuantity": 75
            }
        ]
    }
]
```

## 输出格式

### 成功响应示例
```json
{
    "code": 0,
    "msg": "批量处理完成_总数[2]_成功[2]_失败[0]",
    "traceId": "12345678-1234-1234-1234-123456789012"
}
```

### 部分失败响应示例
```json
{
    "code": 1,
    "msg": "批量处理部分成功_总数[3]_成功[2]_失败[1]_错误详情[物料[INVALID001]:物料编码[goodsCode]不能为空;物料[INVALID002]:物料名称[name]不能为空]",
    "traceId": "12345678-1234-1234-1234-123456789012"
}
```

### 全部失败响应示例
```json
{
    "code": 2,
    "msg": "批量处理失败_总数[2]_成功[0]_失败[2]_错误详情[物料[INVALID001]:物料编码[goodsCode]不能为空;物料[INVALID002]:物资状态[goodsStatus]值2有误，应为0或1]",
    "traceId": "12345678-1234-1234-1234-123456789012"
}
```

## 返回状态码说明

- `code = 0`: 全部成功
- `code = 1`: 部分成功（有成功也有失败）
- `code = 2`: 全部失败或输入解析错误

## 异常处理完善

### 主要异常处理改进：

1. **输入解析异常**：
   - 批量格式解析失败时的详细日志记录
   - 单个格式解析失败时的错误信息记录
   - 两种格式都失败时的明确错误提示

2. **数据库操作异常**：
   - 查询物资信息异常处理
   - 创建物资信息异常处理
   - 更新物资信息异常处理

3. **嵌套处理异常**：
   - 单位信息处理异常（不影响主流程）
   - 品牌信息处理异常（不影响主流程）
   - 单位换算信息处理异常

4. **序列化异常**：
   - 输出参数序列化异常处理
   - FormatResponse 方法序列化异常处理

5. **参数验证异常**：
   - 空参数检查
   - 必填字段验证
   - 数据格式验证

## 业务逻辑保持

- 保持原有的数据库操作逻辑
- 保持原有的参数验证规则
- 保持原有的单位信息和品牌信息处理逻辑
- **增强了错误处理和日志记录机制**

## 向后兼容性

- 完全兼容原有的单个物料信息输入格式
- 原有的调用方式无需修改即可继续使用
- **输出格式保持原有结构不变**，完全兼容现有解析逻辑

## 测试建议

1. 使用 `TestGoodsInfoSyncArray.cs` 中提供的测试数据进行功能验证
2. 测试单个物料信息处理（向后兼容性）
3. 测试批量物料信息处理
4. 测试包含错误数据的批量处理
5. 验证数据库操作的正确性
6. 检查日志记录的完整性

## 性能考虑

- 批量处理时，每个物料信息独立处理，确保单个失败不影响其他物料
- 保持事务的原子性，每个物料的数据库操作独立提交
- 详细的日志记录有助于问题排查和性能监控
