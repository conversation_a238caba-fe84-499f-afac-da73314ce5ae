using SiaSun.LMS.Interface;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.ServiceModel;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true,
                     InstanceContextMode = InstanceContextMode.Single,
                     ConcurrencyMode = ConcurrencyMode.Multiple,
                     MaxItemsInObjectGraph = int.MaxValue)]
    public class S_Plan : I_Plan
    {
        /// <summary>
        /// 获得PLAN模型
        /// </summary>
        public SiaSun.LMS.Model.PLAN_MAIN PlanGetModel(int PLAN_ID)
        {
            return S_Base.sBase.pPLAN_MAIN.GetModel(PLAN_ID);
        }

        /// <summary>
        /// 获得PLAN模型
        /// </summary>
        public SiaSun.LMS.Model.PLAN_DETAIL PlanDetailGetModel(int PLAN_DETAIL_ID)
        {
            return S_Base.sBase.pPLAN_DETAIL.GetModel(PLAN_DETAIL_ID);
        }

        /// <summary>
        /// 获得PLANLIST模型
        /// </summary>
        public SiaSun.LMS.Model.PLAN_LIST PlanListGetModel(int PLAN_LIST_ID)
        {
            return S_Base.sBase.pPLAN_LIST.GetModel(PLAN_LIST_ID);
        }

        /// <summary>创建
        /// 创建
        /// </summary>
        /// <param name="mPLAN_MAIN">计划模型</param>
        /// <param name="lsPLAN_LIST">计划列表</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanCreate(Model.PLAN_MAIN mPLAN_MAIN, IList<Model.PLAN_LIST> lsPLAN_LIST, bool isTrans, out int PLAN_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            PLAN_ID = 0;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(isTrans);

                if (null != S_Base.sBase.pPLAN_MAIN.GetModel(mPLAN_MAIN.PLAN_ID))
                {
                    if (mPLAN_MAIN.PLAN_STATUS.Equals(SiaSun.LMS.Enum.PLAN_STATUS.Complete.ToString()))
                    {
                        bResult = false;
                        sResult = string.Format("{0}单号已经完成 不能编辑!", mPLAN_MAIN.PLAN_CODE);
                        return bResult;
                    }
                    S_Base.sBase.pPLAN_MAIN.Update(mPLAN_MAIN);
                }
                else
                {
                    if (!string.IsNullOrEmpty(mPLAN_MAIN.PLAN_CODE) && null != S_Base.sBase.pPLAN_MAIN.GetModelPlanCode(mPLAN_MAIN.PLAN_CODE))
                    {
                        bResult = false;
                        sResult = string.Format("{0}单号已经存在!", mPLAN_MAIN.PLAN_CODE);
                        return bResult;
                    }
                    S_Base.sBase.pPLAN_MAIN.Add(mPLAN_MAIN);
                }

                foreach (SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
                {
                    bResult = S_Base.sBase.sSystem.GoodsPropertyCheck(mPLAN_LIST.GOODS_ID, mPLAN_LIST, out sResult);
                    if (!bResult)
                    {
                        sResult = string.Format("属性校验错误: {0}", sResult);
                        return bResult;
                    }

                    if (mPLAN_LIST.PLAN_ID.Equals(0))
                    {
                        mPLAN_LIST.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                        S_Base.sBase.pPLAN_LIST.Add(mPLAN_LIST);
                    }
                    else
                    {
                        S_Base.sBase.pPLAN_LIST.Update(mPLAN_LIST);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(isTrans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(isTrans);
                }
            }

            PLAN_ID = mPLAN_MAIN.PLAN_ID;
            return bResult;
        }

        /// <summary>
        /// 出库计划拣配 2020-02-16 21:25
        /// </summary>
        public bool PlanOutListBind(int planListId, List<Model.StorageLockParam> lockParams, string userName, bool trans, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(trans);

                Model.PLAN_LIST mPLAN_LIST = S_Base.sBase.pPLAN_LIST.GetModel(planListId);
                if (mPLAN_LIST == null)
                {
                    result = false;
                    message = $"未找到计划列表_计划列表ID[{planListId}]";
                    return result;
                }

                if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > mPLAN_LIST.PLAN_LIST_QUANTITY)
                {
                    result = false;
                    message = $"计划单已拣配数量[{mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY}]不能大于计划数量[{mPLAN_LIST.PLAN_LIST_QUANTITY}]";
                    return result;
                }

                //List<Model.STORAGE_LIST> lsSTORAGE_LIST = (from r in storageListId select S_Base.sBase.pSTORAGE_LIST.GetModel(r)).ToList();
                DataTable dtSTORAGE_LIST_ENABLE = S_Base.sBase.sDatabase.GetList($"select * from V_STORAGE_LIST where STORAGE_LIST_QUANTITY_UNLOCK > 0 and STORAGE_LIST_ID in ('{string.Join("','", lockParams.Select(r => r.storageListId))}')");
                if (dtSTORAGE_LIST_ENABLE == null || dtSTORAGE_LIST_ENABLE.Rows.Count < 1)
                {
                    result = false;
                    message = $"拣配的源库存列表为空_参数[{string.Join("|", lockParams.Select(r => r.storageListId))}]";
                    return result;
                }

                decimal storageListQtyEnbale = 0;
                if (!decimal.TryParse(dtSTORAGE_LIST_ENABLE.Compute("sum(STORAGE_LIST_QUANTITY_UNLOCK)", "").ToString(), out storageListQtyEnbale) ||
                    lockParams.Sum(r => r.lockQuantity) > storageListQtyEnbale)
                {
                    result = false;
                    message = $"拣配需求总数量[{lockParams.Sum(r => r.lockQuantity)}]不能大于源库存可用数量[{storageListQtyEnbale}]";
                    return result;
                }

                if (dtSTORAGE_LIST_ENABLE.Rows.Count != lockParams.Count)
                {
                    result = false;
                    message = $"拣配的源库存列表行数查询值{dtSTORAGE_LIST_ENABLE.Rows.Count}与传入值{lockParams.Count}不符_参数[{string.Join("|", lockParams.Select(r => r.storageListId))}]";
                    return result;
                }

                if (lockParams.Any(r => r.lockQuantity > decimal.Parse(dtSTORAGE_LIST_ENABLE.Compute("sum(STORAGE_LIST_QUANTITY_UNLOCK)", $"STORAGE_LIST_ID={r.storageListId}").ToString())))
                {
                    result = false;
                    message = $"存在拣配的源库存可用数量小于需求数量的行项目_参数[{string.Join("|", lockParams.Select(r => r.storageListId))}]";
                    return result;
                }

                //if (lockParams.Sum(r => r.lockQuantity) > (mPLAN_LIST.PLAN_LIST_QUANTITY - mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY))
                //{
                //    result = false;
                //    message = $"拣配需求总数量[{lockParams.Sum(r => r.lockQuantity)}]不能大于计划剩余可拣配数量[{mPLAN_LIST.PLAN_LIST_QUANTITY - mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY}]";
                //    return result;
                //}

                foreach (DataRow item in dtSTORAGE_LIST_ENABLE.Rows)
                {
                    int refCount = 0;
                    var lockParam = lockParams.FirstOrDefault(r => r.storageListId.ToString() == item["STORAGE_LIST_ID"].ToString());
                    if (lockParam == null)
                    {
                        result = false;
                        message = $"未找到STORAGE_LIST_ID为[{item["STORAGE_LIST_ID"].ToString()}]的锁定参数";
                        return result;
                    }

                    DataTable dtExistData = S_Base.sBase.sDatabase.GetList($"select STORAGE_LOCK_ID from STORAGE_LOCK where STORAGE_LIST_ID={item["STORAGE_LIST_ID"]} and PLAN_LIST_ID={mPLAN_LIST.PLAN_LIST_ID}");
                    if (dtExistData != null && dtExistData.Rows.Count > 1)
                    {
                        result = false;
                        message = $"计划列表拣配异常_存在{dtExistData.Rows.Count}条库存列表ID[{item["STORAGE_LIST_ID"]}]计划列表[{mPLAN_LIST.PLAN_LIST_ID}]的锁定数据";
                        return result;
                    }
                    else if (dtExistData != null && dtExistData.Rows.Count == 1)
                    {
                        refCount = S_Base.sBase.sDatabase.ExecuteNonQuery($"update STORAGE_LOCK set STORAGE_LOCK_QUANTITY=STORAGE_LOCK_QUANTITY+{lockParam.lockQuantity},STORAGE_LOCK_REMARK=STORAGE_LOCK_REMARK||'绑定{userName}_{Common.StringUtil.GetDateTime()}' where STORAGE_LOCK_ID ={dtExistData.Rows[0][0]}");
                    }
                    else
                    {
                        var insertQuery = $@"insert into STORAGE_LOCK (STORAGE_LOCK_ID,
                                                                       STORAGE_LIST_ID,
                                                                       PLAN_LIST_ID,
                                                                       STORAGE_LOCK_QUANTITY,
                                                                       STORAGE_LOCK_FLAG,
                                                                       DETAIL_FLAG,
                                                                       STORAGE_LOCK_REMARK,
                                                                       BACKUP_FIELD1,
                                                                       BACKUP_FIELD2,
                                                                       BACKUP_FIELD3) 
                                             values (STORAGE_LOCK_SEQ.NEXTVAL,
                                                     {item["STORAGE_LIST_ID"]},
                                                     {mPLAN_LIST.PLAN_LIST_ID},
                                                     {lockParam.lockQuantity},
                                                     '1',
                                                     {lockParam.splitPallet},
                                                     '绑定{userName}_{Common.StringUtil.GetDateTime()}',
                                                     '{lockParam.areaCode}',
                                                     '{lockParam.delayMark}',
                                                     '{lockParam.cartonMark}')";

                        refCount = S_Base.sBase.sDatabase.ExecuteNonQuery(insertQuery);
                    }

                    if (refCount != 1)
                    {
                        result = false;
                        message = $"拣配时写入库存锁定失败_库存列表ID[{item["STORAGE_LIST_ID"]}]";
                        return result;
                    }
                }

                //没有失败说明全部需求已锁定
                mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY += lockParams.Sum(r => r.lockQuantity);
                S_Base.sBase.pPLAN_LIST.Update(mPLAN_LIST);
            }
            catch (Exception ex)
            {
                result = false;
                message = $"拣配异常_信息[{ex.Message}]";
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(trans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(trans);
                }
            }

            return result;
        }

        /// <summary>
        /// 出库计划取消拣配 2020-02-16 21:25
        /// </summary>
        public bool PlanOutListUnBind(string planListId, Dictionary<int, decimal> storageLockDic, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                Model.PLAN_LIST mPLAN_LIST = S_Base.sBase.pPLAN_LIST.GetModel(int.Parse(planListId));
                if (mPLAN_LIST == null)
                {
                    result = false;
                    message = $"未找到计划列表_计划列表ID[{planListId}]";
                    return result;
                }

                foreach (var storageLockPair in storageLockDic)
                {
                    DataTable dtStorageLock = S_Base.sBase.sDatabase.GetList($"select * from STORAGE_LOCK where STORAGE_LOCK_ID={storageLockPair.Key}");
                    if (dtStorageLock == null || dtStorageLock.Rows.Count != 1)
                    {
                        result = false;
                        message = $"获取锁定库存失败_锁定ID[{storageLockPair.Key}]";
                        return result;
                    }

                    if (planListId != dtStorageLock.Rows[0]["PLAN_LIST_ID"].ToString())
                    {
                        result = false;
                        message = $"计划列表ID不相符_参数[{planListId}]_数据库[{dtStorageLock.Rows[0]["PLAN_LIST_ID"]}]";
                        return result;
                    }

                    decimal storageLockQty = decimal.Parse(dtStorageLock.Rows[0]["STORAGE_LOCK_QUANTITY"].ToString());
                    decimal unBindQty = storageLockPair.Value;
                    if (unBindQty != -1)
                    {
                        if (unBindQty > storageLockQty)
                        {
                            result = false;
                            message = $"解锁数量[{unBindQty}]不能大于已锁定数量[{storageLockQty}]";
                            return result;
                        }
                    }
                    else
                    {
                        unBindQty = storageLockQty;
                    }

                    int refCount = 0;
                    if (unBindQty == storageLockQty)
                    {
                        refCount = S_Base.sBase.sDatabase.ExecuteNonQuery($"delete from STORAGE_LOCK where STORAGE_LOCK_ID={storageLockPair.Key}");
                    }
                    else if (unBindQty < storageLockQty)
                    {
                        refCount = S_Base.sBase.sDatabase.ExecuteNonQuery($"update STORAGE_LOCK set STORAGE_LOCK_QUANTITY={storageLockQty - unBindQty} where STORAGE_LOCK_ID={storageLockPair.Key}");
                    }
                    else
                    {
                        result = false;
                        message = $"解锁数量[{unBindQty}]不能大于已锁定数量[{storageLockQty}]";
                        return result;
                    }

                    if (refCount != 1)
                    {
                        result = false;
                        message = $"拣配解绑更新库存锁定失败_库存锁定ID[{storageLockPair.Key}]";
                        return result;
                    }

                    mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY -= unBindQty;
                }
                S_Base.sBase.pPLAN_LIST.Update(mPLAN_LIST);
            }
            catch (Exception ex)
            {
                result = false;
                message = $"拣配解绑异常_信息[{ex.Message}]";
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction();
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction();
                }
            }

            return result;
        }

        /// <summary>完成
        /// 完成
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanComplete(int PLAN_ID, out string sResult)
        {
            return this.PlanComplete(PLAN_ID, true, out sResult);
        }

        /// <summary>
        /// 计划执行
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanExecute(int PLAN_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = S_Base.sBase.pPLAN_MAIN.GetModel(PLAN_ID);

            if (null == mPLAN_MAIN)
            {
                bResult = false;

                sResult = string.Format("未找到计划{0}", PLAN_ID);

                return bResult;
            }
            try
            {
                S_Base.sBase.sDatabase.BeginTransaction();

                if (string.IsNullOrEmpty(mPLAN_MAIN.PLAN_BEGIN_TIME))
                    mPLAN_MAIN.PLAN_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                mPLAN_MAIN.PLAN_STATUS = SiaSun.LMS.Enum.PLAN_STATUS.Executing.ToString();

                S_Base.sBase.pPLAN_MAIN.Update(mPLAN_MAIN);


                S_Base.sBase.sDatabase.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                S_Base.sBase.sDatabase.RollBackTransaction();
            }

            return bResult;
        }

        /// <summary>
        /// 出库计划自动选定货位出库
        /// 关于终点位置选择：
        /// </summary>
        /// <param name="planId"></param>
        /// <param name="operatorName"></param>
        /// <param name="trans"></param>
        /// <param name="reActPalletEndCellCode">重新指定的整托出库终点位置</param>
        /// <param name="sResult"></param>
        /// <param name="actionArea">下达哪一个库区的出库任务</param>
        /// <returns></returns>
        public bool PlanOutDownLoad(int planId, string operatorName, bool trans, string reActPalletEndCellCode, out string sResult, string actionArea = "", string actionBindId = "")
        {
            bool bResult = true;
            sResult = string.Empty;
            //标记是否下达任务来更新计划状态为“执行中”
            bool isCreateManage = false;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(trans);

                Model.PLAN_MAIN mPLAN_MAIN = S_Base.sBase.pPLAN_MAIN.GetModel(planId);
                if (mPLAN_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到计划信息_计划ID[{0}]", planId);
                    return bResult;
                }
                if (mPLAN_MAIN.PLAN_STATUS == Enum.PLAN_STATUS.Cancel.ToString())
                {
                    bResult = false;
                    sResult = string.Format("计划已取消无法下达_计划编码[{0}]", mPLAN_MAIN.PLAN_CODE);
                    return bResult;
                }
                if (mPLAN_MAIN.PLAN_STATUS == Enum.PLAN_STATUS.Complete.ToString())
                {
                    return bResult;
                }

                Model.PLAN_TYPE mPLAN_TYPE = S_Base.sBase.pPLAN_TYPE.GetModelPlanTypeCode(mPLAN_MAIN.PLAN_TYPE_CODE);
                if (mPLAN_TYPE == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到计划类型_计划ID[{0}]", planId);
                    return bResult;
                }

                IList<Model.PLAN_LIST> lsPLAN_LIST = S_Base.sBase.pPLAN_LIST.GetListPlanID(planId);
                if (lsPLAN_LIST == null || lsPLAN_LIST.Count < 1)
                {
                    bResult = false;
                    sResult = string.Format("未找到计划行项目_计划ID[{0}]", planId);
                    return bResult;
                }

                #region 确定出库口

                int pickAreaEndCellId = 0;
                int commonEndCellId = 0;

                //本次出库涉及到除分拣区之外的物料，则要配置常规出库口
                if (string.IsNullOrEmpty(actionArea) || actionArea.Contains(Enum.AreaCode.Product.ToString()) ||
                    actionArea.Contains(Enum.AreaCode.Battery.ToString()) || actionArea.Contains(Enum.AreaCode.ZCQ.ToString()))
                {
                    if (!string.IsNullOrEmpty(reActPalletEndCellCode))
                    {
                        //配置了出库口重指向
                        var endCell = S_Base.sBase.pWH_CELL.GetModel("", reActPalletEndCellCode);
                        if (endCell == null)
                        {
                            bResult = false;
                            sResult = string.Format("未找到整托出库重指向参数reActPalletEndCellCode[{0}]对应的站台信息", reActPalletEndCellCode);
                            return bResult;
                        }

                        commonEndCellId = endCell.CELL_ID;
                    }
                    else
                    {
                        //未配置了出库口重指向并且不是根据LIST出库
                        if (mPLAN_MAIN.PLAN_INOUT_STATION != Enum.KeyWords.ByList.ToString())
                        {
                            var endCell = S_Base.sBase.pWH_CELL.GetModel("", mPLAN_MAIN.PLAN_INOUT_STATION);
                            if (endCell == null)
                            {
                                //如果根据 mPLAN_MAIN.PLAN_INOUT_STATION 字段不能得到出库站台，则需要考虑工厂+库存地点匹配
                                var querySql = $"select * from WH_CELL where CELL_TYPE = 'Station' and CELL_PROPERTY like '{mPLAN_MAIN.PLAN_FROM_USER}'";
                                var dtEndStationByCellProperty = S_Base.sBase.sDatabase.GetList(querySql);

                                if (dtEndStationByCellProperty != null && dtEndStationByCellProperty.Rows.Count > 0)
                                {
                                    commonEndCellId = int.Parse(dtEndStationByCellProperty.Rows[0]["CELL_ID"].ToString());
                                }
                            }
                            else
                            {
                                commonEndCellId = endCell.CELL_ID;
                            }
                        }
                    }
                }

                #endregion

                foreach (Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
                {
                    if (mPLAN_MAIN.PLAN_INOUT_STATION == Enum.KeyWords.ByList.ToString())
                    {
                        var mWH_CELL_END = S_Base.sBase.pWH_CELL.GetModel(string.Empty, mPLAN_LIST.OUT_POSITION);
                        if (mWH_CELL_END == null)
                        {
                            bResult = false;
                            sResult = string.Format("计划站台信息有误_值[{0}]", mPLAN_LIST.OUT_POSITION);
                            return bResult;
                        }

                        commonEndCellId = mWH_CELL_END.CELL_ID;
                    }
                    if (commonEndCellId == 0)
                    {
                        bResult = false;
                        sResult = string.Format("出库站台未确定");
                        return bResult;
                    }

                    bResult = PlanOutListDownLoad(mPLAN_LIST, mPLAN_MAIN, commonEndCellId, operatorName, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    //任务下达标记
                    isCreateManage = true;
                }


                //更新计划状态
                if (isCreateManage)
                {
                    //mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Download.ToString();
                    mPLAN_MAIN.PLAN_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    S_Base.sBase.pPLAN_MAIN.Update(mPLAN_MAIN);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("按计划出库异常_信息[{0}]", ex.ToString());
            }
            finally
            {
                if (bResult)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(trans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(trans);
                }
            }
            return bResult;
        }

        /// <summary>
        /// 计划暂停
        /// </summary>
        public bool PlanPause(int planId, string operatorName, bool trans, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(trans);

                var planMain = S_Base.sBase.pPLAN_MAIN.GetModel(planId);
                if (planMain == null)
                {
                    result = false;
                    message = $"未找到ID为[{planId}]的计划信息";
                    return result;
                }
                if (planMain.PLAN_STATUS == Enum.PLAN_STATUS.Waiting.ToString())
                {
                    planMain.BACKUP_FIELD3 = planMain.PLAN_STATUS;
                    planMain.PLAN_STATUS = Enum.PLAN_STATUS.Pause.ToString();
                    S_Base.sBase.pPLAN_MAIN.Update(planMain);

                    var manageMainList = S_Base.sBase.pMANAGE_MAIN.GetListPlanID(planMain.PLAN_ID);
                    if (manageMainList != null && manageMainList.Count > 0)
                    {
                        foreach (var manageMain in manageMainList)
                        {
                            //有控制任务 只处理等待状态的任务
                            var ioControl = S_Base.sBase.pIO_CONTROL.GetModelManageID(manageMain.MANAGE_ID);
                            if (ioControl != null &&
                                ioControl.CONTROL_STATUS == (int)Enum.CONTROL_STATUS.Wait &&
                                manageMain.MANAGE_STATUS == Enum.MANAGE_STATUS.Waiting.ToString())
                            {
                                manageMain.MANAGE_STATUS = Enum.MANAGE_STATUS.Pause.ToString();
                                manageMain.MANAGE_REMARK += $" [{operatorName}]暂停_{Common.StringUtil.GetDateTime()}";
                                S_Base.sBase.pMANAGE_MAIN.Update(manageMain);

                                ioControl.CONTROL_STATUS = (int)Enum.CONTROL_STATUS.Pause;
                                ioControl.CONTROL_REMARK += $" [{operatorName}]暂停_{Common.StringUtil.GetDateTime()}";
                                S_Base.sBase.pIO_CONTROL.Update(ioControl);
                            }

                        }
                    }
                }
                else
                {
                    result = false;
                    message = $"计划[{planMain.PLAN_CODE}]状态[{planMain.PLAN_STATUS}]无法[暂停]操作";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(trans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(trans);
                }
            }

            return result;
        }

        /// <summary>
        /// 计划取消暂停
        /// </summary>
        public bool PlanCancelPause(int planId, string operatorName, bool trans, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(trans);

                var planMain = S_Base.sBase.pPLAN_MAIN.GetModel(planId);
                if (planMain == null)
                {
                    result = false;
                    message = $"未找到ID为[{planId}]的计划信息";
                    return result;
                }
                if (planMain.PLAN_STATUS == Enum.PLAN_STATUS.Pause.ToString())
                {
                    planMain.PLAN_STATUS = planMain.BACKUP_FIELD3;
                    S_Base.sBase.pPLAN_MAIN.Update(planMain);

                    var manageMainList = S_Base.sBase.pMANAGE_MAIN.GetListPlanID(planMain.PLAN_ID);
                    if (manageMainList != null && manageMainList.Count > 0)
                    {
                        foreach (var manageMain in manageMainList)
                        {
                            //包含控制任务的处于暂停状态的任务
                            var ioControl = S_Base.sBase.pIO_CONTROL.GetModelManageID(manageMain.MANAGE_ID);
                            if (ioControl.CONTROL_STATUS == (int)Enum.CONTROL_STATUS.Pause
                                && manageMain.MANAGE_STATUS == Enum.MANAGE_STATUS.Pause.ToString())
                            {
                                manageMain.MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString();
                                manageMain.MANAGE_REMARK += $" [{operatorName}]取消暂停_{Common.StringUtil.GetDateTime()}";
                                S_Base.sBase.pMANAGE_MAIN.Update(manageMain);

                                ioControl.CONTROL_STATUS = (int)Enum.CONTROL_STATUS.Wait;
                                ioControl.CONTROL_REMARK += $" [{operatorName}]取消暂停_{Common.StringUtil.GetDateTime()}";
                                S_Base.sBase.pIO_CONTROL.Update(ioControl);
                            }
                        }
                    }
                }
                else
                {
                    result = false;
                    message = $"计划[{planMain.PLAN_CODE}]状态[{planMain.PLAN_STATUS}]无法[取消暂停]操作";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(trans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(trans);
                }
            }

            return result;
        }

        /// <summary>
        /// 计划取消
        /// </summary>
        public bool PlanCancel(int planId, string operatorName, bool trans, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(trans);

                var planMain = S_Base.sBase.pPLAN_MAIN.GetModel(planId);
                if (planMain == null)
                {
                    result = false;
                    message = $"未找到ID为[{planId}]的计划信息";
                    return result;
                }
                if (planMain.PLAN_STATUS == Enum.PLAN_STATUS.Waiting.ToString())
                {
                    var planLists = S_Base.sBase.pPLAN_LIST.GetListPlanID(planMain.PLAN_ID);

                    //有完成数量不允许取消
                    if (planLists.Sum(r => r.PLAN_LIST_FINISHED_QUANTITY) > 0)
                    {
                        result = false;
                        message = $"计划[{planMain.PLAN_CODE}]已经有完成数量_无法取消";
                        return result;
                    }

                    var emptyPalletGoodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(Enum.KeyWords.emptyPallet.ToString());
                    if (emptyPalletGoodsMain == null)
                    {
                        result = false;
                        message = $"未找到空托盘物料主数据";
                        return result;
                    }


                    //遍历计划列表
                    foreach (var item in planLists)
                    {
                        var dtManageIds = S_Base.sBase.sDatabase.GetList($"select distinct MANAGE_ID from MANAGE_LIST where PLAN_LIST_ID ={item.PLAN_LIST_ID}");
                        if (dtManageIds != null && dtManageIds.Rows.Count > 0)
                        {
                            foreach (DataRow manageIdItem in dtManageIds.Rows)
                            {
                                if (!int.TryParse(manageIdItem["MANAGE_ID"].ToString(), out int manageId))
                                {
                                    result = false;
                                    message = $"任务ID[{manageIdItem["MANAGE_ID"].ToString()}]转换失败_计划[{planMain.PLAN_CODE}]";
                                    return result;
                                }

                                var manageMain = S_Base.sBase.pMANAGE_MAIN.GetModel(manageId);
                                if (manageMain != null)
                                {
                                    if (manageMain.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManageOut.ToString()
                                        && manageMain.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManagePick.ToString())
                                    {
                                        result = false;
                                        message = $"任务类型[{manageMain.MANAGE_TYPE_CODE}]不在预期范围内_托盘条码[{manageMain.STOCK_BARCODE}]_计划[{planMain.PLAN_CODE}]";
                                        return result;
                                    }

                                    //整托区 直接将任务取消掉等待执行的任务
                                    if (manageMain.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageOut.ToString())
                                    {
                                        var ioControl = S_Base.sBase.sDatabase.GetList($"select * from IO_CONTROL where MANAGE_ID = {manageMain.MANAGE_ID} for update");

                                        //没有控制任务 或者 控制任务状态是等待状态，直接取消任务，否则报错
                                        if (ioControl != null && ioControl.Rows.Count > 0 && ioControl.Rows[0]["CONTROL_STATUS"].ToString() != Enum.CONTROL_STATUS.Wait.ToString("d"))
                                        {
                                            result = false;
                                            message = $"任务对应的控制任务数量或者状态有误_托盘条码[{manageMain.STOCK_BARCODE}]_计划[{planMain.PLAN_CODE}]";
                                            return result;
                                        }
                                    }
                                    result = S_Base.sBase.sManage.ManageCancel(manageMain.MANAGE_ID, out message, false);
                                    if (!result)
                                    {
                                        message = $"取消任务失败_信息[{message}]托盘条码[{manageMain.STOCK_BARCODE}]_计划[{planMain.PLAN_CODE}]";
                                        return result;
                                    }
                                }
                            }
                        }

                    }

                    planMain.PLAN_STATUS = Enum.PLAN_STATUS.Cancel.ToString();
                    planMain.PLAN_REMARK += $" [{operatorName}]取消计划_{Common.StringUtil.GetDateTime()}";
                    S_Base.sBase.pPLAN_MAIN.Update(planMain);

                }
                else
                {
                    result = false;
                    message = $"计划[{planMain.PLAN_CODE}]状态[{planMain.PLAN_STATUS}]无法[取消]操作";
                    return result;
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"异常_信息[{ex.Message}]";
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(trans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(trans);
                }
            }

            return result;
        }

        /// <summary>
        /// 检验计划是否可以完成
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        internal bool PlanCheckComplete(int PLAN_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = S_Base.sBase.pPLAN_MAIN.GetModel(PLAN_ID);

            if (null == mPLAN_MAIN)
            {
                bResult = false;

                sResult = string.Format("未找到计划{0}", PLAN_ID.ToString());

                return bResult;
            }

            IList<SiaSun.LMS.Model.PLAN_LIST> lsPLAN_LIST = S_Base.sBase.pPLAN_LIST.GetListNoComplete(PLAN_ID);


            if (lsPLAN_LIST.Count <= 0)
            {
                bResult = false;

                return bResult;
            }


            return bResult;
        }

        /// <summary>
        /// 完成-支持事务
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        internal bool PlanComplete(int planId, bool raiseTrans, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                S_Base.sBase.sDatabase.BeginTransaction(raiseTrans);

                Model.PLAN_MAIN planMain = S_Base.sBase.pPLAN_MAIN.GetModel(planId);
                if (planMain == null)
                {
                    result = false;
                    message = $"未找到计划_计划ID[{planId}]";
                    return result;
                }

                if (planMain.PLAN_STATUS == Enum.PLAN_STATUS.Complete.ToString())
                {
                    return result;
                }

                Model.PLAN_TYPE planType = S_Base.sBase.pPLAN_TYPE.GetModelPlanTypeCode(planMain.PLAN_TYPE_CODE);
                if (planType == null)
                {
                    result = false;
                    message = $"未找到计划类型_计划单号[{planMain.PLAN_CODE}]";
                    return result;
                }

                planMain.PLAN_END_TIME = Common.StringUtil.GetDateTime();
                planMain.PLAN_STATUS = Enum.PLAN_STATUS.Complete.ToString();
                S_Base.sBase.pPLAN_MAIN.Update(planMain);

                // 如果是盘点计划，调用盘点结果上报接口
                if (planType.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCommonDown.ToString())
                {
                    bool inventoryResult = CallInventoryResultCallback(planMain, out string inventoryMessage);
                    if (!inventoryResult)
                    {
                        // 盘点结果上报失败不影响计划完成，只记录日志
                        S_Base.sBase.Log.Warn($"盘点结果上报失败_计划ID[{planMain.PLAN_ID}]_计划编码[{planMain.PLAN_CODE}]_信息[{inventoryMessage}]");
                    }
                    else
                    {
                        S_Base.sBase.Log.Info($"盘点结果上报成功_计划ID[{planMain.PLAN_ID}]_计划编码[{planMain.PLAN_CODE}]");
                    }
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"计划完成异常_信息[{ex.Message}]";
            }
            finally
            {
                if (result)
                {
                    S_Base.sBase.sDatabase.CommitTransaction(raiseTrans);
                }
                else
                {
                    S_Base.sBase.sDatabase.RollBackTransaction(raiseTrans);
                }
            }
            return result;
        }

        /// <summary>
        /// 调用盘点结果回调接口
        /// </summary>
        /// <param name="planMain">盘点计划主表信息</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        private bool CallInventoryResultCallback(Model.PLAN_MAIN planMain, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 获取盘点计划的明细列表
                IList<Model.PLAN_LIST> planLists = S_Base.sBase.pPLAN_LIST.GetListPlanID(planMain.PLAN_ID);
                if (planLists == null || planLists.Count == 0)
                {
                    result = false;
                    message = "未找到盘点计划明细信息";
                    return result;
                }

                // 生成盘点任务相关信息（如果没有保存，则生成默认值）
                string stockTakeId = planMain.BACKUP_FIELD1 ?? string.Empty;
                string stockTakeName = planMain.BACKUP_FIELD2 ?? string.Empty;
                string stockTakeCode = planMain.PLAN_CODE ?? string.Empty;

                // 构建盘点结果明细列表
                var stockTakeResultGoodsList = new List<Interface.iWMS.InventoryResultCallback.StockTakeResultGoodsItem>();

                foreach (var planList in planLists)
                {
                    // 获取物料信息
                    var goodsMain = S_Base.sBase.pGOODS_MAIN.GetModel(planList.GOODS_ID);
                    if (goodsMain == null)
                    {
                        S_Base.sBase.Log.Warn($"未找到物料信息_物料ID[{planList.GOODS_ID}]");
                        continue;
                    }

                    

                    var resultItem = new Interface.iWMS.InventoryResultCallback.StockTakeResultGoodsItem
                    {
                        // 必填字段
                        goodsId = goodsMain.GOODS_ID.ToString(),
                        storageNum = (int)planList.PLAN_LIST_QUANTITY, // 库存数量
                        stockTakeNum = (int)planList.PLAN_LIST_FINISHED_QUANTITY, // 盘点数量

                    };

                    stockTakeResultGoodsList.Add(resultItem);
                }

                if (stockTakeResultGoodsList.Count == 0)
                {
                    result = false;
                    message = "没有有效的盘点结果数据";
                    return result;
                }

                // 调用盘点结果回调接口
                var inventoryCallback = new Interface.iWMS.InventoryResultCallback();
                result = inventoryCallback.IntefaceMethod(
                    stockTakeId, // 盘点计划ID
                    stockTakeName, // 盘点计划名称
                    stockTakeCode, // 盘点计划编码
                    stockTakeResultGoodsList,
                    out message);

                return result;
            }
            catch (Exception ex)
            {
                result = false;
                message = $"调用盘点结果回调接口异常：{ex.Message}";
                S_Base.sBase.Log.Error($"CallInventoryResultCallback异常：{ex.Message}", ex);
                return result;
            }
        }

        /// <summary>
        /// 出库计划单执行
        /// </summary>
        private bool PlanOutListDownLoad(Model.PLAN_LIST mPLAN_LIST, Model.PLAN_MAIN mPLAN_MAIN, int endCellId, string operatorName, out string message)
        {
            bool result = true;
            message = string.Empty;

            Model.GOODS_MAIN mGOODS_MAIN = S_Base.sBase.pGOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID);
            if (mGOODS_MAIN == null)
            {
                result = false;
                message = string.Format("未找到计划物料_物料ID[{0}]_计划单ID[{1}]", mPLAN_LIST.GOODS_ID, mPLAN_LIST.PLAN_LIST_ID);
                return result;
            }

            Model.WH_CELL endCell = S_Base.sBase.pWH_CELL.GetModel(endCellId);
            if (endCell == null)
            {
                result = false;
                message = string.Format("未找到ID[{0}]的货位信息", endCellId);
                return result;
            }

            string propertySql = S_Base.sBase.sSystem.GetGoodsPropertySql(mPLAN_LIST.GOODS_ID, mPLAN_LIST, Enum.MatchMode.IgnoreNull);

            var dtEnableStorage = S_Base.sBase.sSystem.GetEnableStorage(mPLAN_LIST.GOODS_ID, propertySql, endCell.CELL_CODE);
            if (dtEnableStorage == null || dtEnableStorage.Rows.Count < 1)
            {
                result = false;
                message = "可用库存不足(可能原因:库存不足/设备报警/货位状态异常)";
                return result;
            }

            //按垛出库的时候PLAN_LIST_QUANTITY < 0
            bool planOutByStackCount = mPLAN_LIST.PLAN_LIST_QUANTITY < 0;
            decimal stackCount = 0;

            //校验库存数量是否充足
            if (planOutByStackCount)
            {
                //按垛出库
                if (dtEnableStorage.Rows.Count < stackCount)
                {
                    result = false;
                    message = string.Format("计划执行失败_计划出库垛数量为[{0}]_可用垛数量[{1}]_物料编码[{2}]", stackCount, dtEnableStorage.Rows.Count, mGOODS_MAIN.GOODS_CODE);
                    return result;
                }
            }
            else
            {
                //按数量出库
                decimal sumQuantity = Convert.ToDecimal(dtEnableStorage.Compute("sum(LANE_WAY_QUANTITY_UNLOCK)", ""));
                result = sumQuantity >= mPLAN_LIST.PLAN_LIST_QUANTITY - mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY;
                if (!result)
                {
                    message = string.Format("计划执行失败_计划出库数量为[{1}]_当前可用库存为[{0}]_物料编码[{2}]", sumQuantity, mPLAN_LIST.PLAN_LIST_QUANTITY.ToString(), mGOODS_MAIN.GOODS_CODE);
                    return result;
                }
            }

            //开始下任务
            for (int i = 0; i < dtEnableStorage.Rows.Count; i++)
            {
                if (mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY >= Math.Abs(mPLAN_LIST.PLAN_LIST_QUANTITY))
                {
                    break;
                }

                string stockBarcode = dtEnableStorage.Rows[i]["STOCK_BARCODE"].ToString();

                Model.STORAGE_MAIN mSTORAGE_MAIN = S_Base.sBase.pSTORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if (mSTORAGE_MAIN == null)
                {
                    result = false;
                    message = string.Format("未找到库存_托盘条码[{0}]", stockBarcode);
                    return result;
                }
                IList<Model.STORAGE_LIST> lsSTORAGE_LIST = S_Base.sBase.pSTORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                {
                    result = false;
                    message = string.Format("未找到库存列表_托盘条码[{0}]", stockBarcode);
                    return result;
                }

                //素电自动检验要求库存列表单一
                if (endCell.CELL_CODE == S_Base.sBase.sSystem.GetSysParameter("BatteryAutoCheckStation", "42829") &&
                    lsSTORAGE_LIST.Count != 1)
                {
                    result = false;
                    message = string.Format("出库到素电自动检验站台的库存要求组盘单一物料_托盘条码[{0}]", stockBarcode);
                    return result;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = S_Base.sBase.pMANAGE_MAIN.GetModelStockBarcode(mSTORAGE_MAIN.STOCK_BARCODE);
                if (mMANAGE_MAIN != null)
                {
                    result = false;
                    message = string.Format("条码存在任务_无法出库_托盘条码[{0}]", mSTORAGE_MAIN.STOCK_BARCODE);
                    return result;
                }

                mMANAGE_MAIN = new Model.MANAGE_MAIN();
                mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                mMANAGE_MAIN.END_CELL_ID = endCellId;
                mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                mMANAGE_MAIN.MANAGE_LEVEL = mPLAN_MAIN.PLAN_LEVEL;
                mMANAGE_MAIN.MANAGE_OPERATOR = operatorName;
                mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                mMANAGE_MAIN.MANAGE_REMARK = "";
                mMANAGE_MAIN.MANAGE_SOURCE = "";
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Waiting.ToString();
                mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageOut.ToString();
                mMANAGE_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                mMANAGE_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;

                List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                {
                    Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                    mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemSTORAGE_LIST, mMANAGE_LIST, null);
                    mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                    if (itemSTORAGE_LIST.GOODS_ID == mPLAN_LIST.GOODS_ID)
                    {
                        mMANAGE_LIST.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                        mMANAGE_LIST.STORAGE_LIST_ID = itemSTORAGE_LIST.STORAGE_LIST_ID;
                    }
                    lsMANAGE_LIST.Add(mMANAGE_LIST);
                }

                result = S_Base.sBase.sManage.ManageCreate(
                    mMANAGE_MAIN,
                    lsMANAGE_LIST,
                    raiseTrans: false,
                    checkStorage: true,
                    checkManage: true,
                    checkCellStatus: true,
                    autoComplete: false,
                    autoControl: true,
                    doubleInAutoMove: true,
                    out message);

                if (!result)
                {
                    return result;
                }

                //更新计划数量
                if (planOutByStackCount)
                {
                    mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY++;
                    mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY += lsMANAGE_LIST.Sum(r => r.MANAGE_LIST_QUANTITY);
                }
                else
                {
                    mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += lsMANAGE_LIST.Sum(r => r.MANAGE_LIST_QUANTITY);
                }

                S_Base.sBase.pPLAN_LIST.Update(mPLAN_LIST);
            }

            //校验任务数量是否下达充足
            if (planOutByStackCount)
            {
                if (Math.Abs(mPLAN_LIST.PLAN_LIST_QUANTITY) != mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                {
                    result = false;
                    message = string.Format("按垛出库计划执行失败_计划数量[{0}]与分配数量[{1}]不相等_物料编码[{2}]",
                        Math.Abs(mPLAN_LIST.PLAN_LIST_QUANTITY), mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY, mGOODS_MAIN.GOODS_CODE);
                    return result;
                }
                else
                {
                    //按垛出库校验通过后将计划垛数量更新为实际的数量
                    mPLAN_LIST.PLAN_LIST_QUANTITY = mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY;
                    mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY;
                    mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY = 0;
                    S_Base.sBase.pPLAN_LIST.Update(mPLAN_LIST);
                }
            }
            else
            {
                if (mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY < mPLAN_LIST.PLAN_LIST_QUANTITY)
                {
                    result = false;
                    message = string.Format("按数量出库计划执行失败_计划数量[{0}]大于分配数量[{1}]_物料编码[{2}]",
                        mPLAN_LIST.PLAN_LIST_QUANTITY, mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY, mGOODS_MAIN.GOODS_CODE);
                    return result;
                }
            }

            return result;
        }
    }
}
