using System;
using System.Collections.Generic;
using System.Linq;
using SiaSun.LMS.Model;
using SiaSun.LMS.Implement.Interface.iWMS;

namespace SiaSun.LMS.Implement.Interface.MaterialSystem
{
    /// <summary>
    /// 移库任务物资系统接口调用策略
    /// </summary>
    public class TransferInterfaceStrategy : IMaterialSystemInterfaceStrategy
    {
        public string StrategyName => "移库任务接口策略";

        /// <summary>
        /// 调用移库结果回调接口
        /// </summary>
        /// <param name="manageMain">任务主表信息</param>
        /// <param name="manageList">任务明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool CallInterface(MANAGE_MAIN manageMain, IList<MANAGE_LIST> manageList, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 参数校验
                if (manageMain == null)
                {
                    result = false;
                    message = "任务主表信息不能为空";
                    return result;
                }

                if (manageList == null || manageList.Count == 0)
                {
                    result = false;
                    message = "任务明细列表不能为空";
                    return result;
                }

                // 获取起始货位信息
                var startCell = S_Base.sBase.pWH_CELL.GetModel(manageMain.START_CELL_ID);
                if (startCell == null)
                {
                    result = false;
                    message = $"未找到起始货位信息_货位ID[{manageMain.START_CELL_ID}]";
                    return result;
                }

                // 获取目标货位信息
                var endCell = S_Base.sBase.pWH_CELL.GetModel(manageMain.END_CELL_ID);
                if (endCell == null)
                {
                    result = false;
                    message = $"未找到目标货位信息_货位ID[{manageMain.END_CELL_ID}]";
                    return result;
                }

                // 获取起始仓库信息
                var startArea = S_Base.sBase.pWH_AREA.GetModel(startCell.AREA_ID);
                if (startArea == null)
                {
                    result = false;
                    message = $"未找到起始货位所属区域信息_区域ID[{startCell.AREA_ID}]";
                    return result;
                }

                // 获取目标仓库信息
                var endArea = S_Base.sBase.pWH_AREA.GetModel(endCell.AREA_ID);
                if (endArea == null)
                {
                    result = false;
                    message = $"未找到目标货位所属区域信息_区域ID[{endCell.AREA_ID}]";
                    return result;
                }

                // 构建移库结果明细列表
                var transferItems = new List<TransferResultCallback.TransferResultItem>();

                foreach (var manageItem in manageList)
                {
                    // 获取物料信息
                    var goods = S_Base.sBase.pGOODS_MAIN.GetModel(manageItem.GOODS_ID);

                    var storage = S_Base.sBase.pSTORAGE_LIST.GetModel(manageItem.STORAGE_LIST_ID);
                    
                    var transferItem = new TransferResultCallback.TransferResultItem
                    {
                        removeNum = (int)manageItem.MANAGE_LIST_QUANTITY,
                        goodsCode = goods?.GOODS_CODE ?? manageItem.GOODS_ID.ToString(),
                        
                        // 原货位信息
                        warehouseCode = "1",
                        warehouseName = "立体库",
                        warehouseId = string.Empty,
                        shelfCode = startCell.CELL_CODE.Contains("1200") ? "ZCQ01" : startCell.CELL_CODE,
                        shelfName = startCell.CELL_CODE.Contains("1200") ? "ZCQ01" : startCell.CELL_CODE,
                        shelfId = string.Empty,
                        brand = manageItem.GOODS_PROPERTY1 ?? string.Empty,
                        lineId = manageItem.GOODS_PROPERTY2 ?? string.Empty,
                        
                        // 目标货位信息
                        targetWarehouseCode = "1",
                        targetWarehouseName = "立体库",
                        targetWarehouseId = string.Empty,
                        targetShelfCode = endCell.CELL_CODE.Contains("1200") ? "ZCQ01" : endCell.CELL_CODE,
                        targetShelfName = endCell.CELL_CODE.Contains("1200") ? "ZCQ01" : endCell.CELL_CODE,
                        targetShelfId = string.Empty
                    };

                    transferItems.Add(transferItem);
                }

                // 调用移库结果回调接口
                var callback = new TransferResultCallback();
                result = callback.IntefaceMethod(transferItems, out message);

                if (result)
                {
                    S_Base.sBase.Log.Info($"移库结果接口调用成功_任务ID[{manageMain.MANAGE_ID}]_托盘条码[{manageMain.STOCK_BARCODE}]_明细数量[{transferItems.Count}]_从[{startArea.AREA_CODE}-{startCell.CELL_CODE}]到[{endArea.AREA_CODE}-{endCell.CELL_CODE}]");
                }
                else
                {
                    S_Base.sBase.Log.Error($"移库结果接口调用失败_任务ID[{manageMain.MANAGE_ID}]_托盘条码[{manageMain.STOCK_BARCODE}]_错误信息[{message}]");
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"移库结果接口调用异常：{ex.Message}";
                S_Base.sBase.Log.Error($"移库结果接口调用异常_任务ID[{manageMain?.MANAGE_ID}]_异常信息[{ex.Message}]", ex);
            }

            return result;
        }
    }
}
