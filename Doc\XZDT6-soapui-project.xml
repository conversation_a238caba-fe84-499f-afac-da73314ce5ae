<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="59715282-559c-46ab-aa0f-efebd66c17c6" activeEnvironment="Default" name="XZDT6" resourceRoot="" soapui-version="5.9.0" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:WsdlInterface" id="f90443bf-b5fc-4d64-a6c9-58b16328dda7" wsaVersion="NONE" name="BasicHttpBinding_I_Interface" type="wsdl" bindingName="{http://tempuri.org/}BasicHttpBinding_I_Interface" soapVersion="1_1" anonymous="optional" definition="http://127.0.0.1:8001/Service/Interface?singleWsdl" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="http://127.0.0.1:8001/Service/Interface?singleWsdl"><con:part><con:url>http://127.0.0.1:8001/Service/Interface?singleWsdl</con:url><con:content><![CDATA[<wsdl:definitions name="S_Interface" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <wsdl:types>
    <xs:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
      <xs:element name="GoodsInfoSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GoodsInfoSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="GoodsInfoSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="UnitInfoSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="UnitInfoSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="UnitInfoSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="WarehouseInfoSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="WarehouseInfoSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="WarehouseInfoSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="OrganizationalStructureInfoSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="OrganizationalStructureInfoSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="OrganizationalStructureInfoSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ShelfSpaceSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="ShelfSpaceSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="ShelfSpaceSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="InboundReceiptSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="InboundReceiptSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="InboundReceiptSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="InventoryReversalReceiptSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="InventoryReversalReceiptSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="InventoryReversalReceiptSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="OutboundReceiptSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="OutboundReceiptSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="OutboundReceiptSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GoodsIssueSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GoodsIssueSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="GoodsIssueSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GoodsIssueReversalReceiptSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="GoodsIssueReversalReceiptSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="GoodsIssueReversalReceiptSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="InventoryPlanSync">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="inputJson" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
      <xs:element name="InventoryPlanSyncResponse">
        <xs:complexType>
          <xs:sequence>
            <xs:element minOccurs="0" name="InventoryPlanSyncResult" nillable="true" type="xs:string"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:schema>
    <xs:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/">
      <xs:element name="anyType" nillable="true" type="xs:anyType"/>
      <xs:element name="anyURI" nillable="true" type="xs:anyURI"/>
      <xs:element name="base64Binary" nillable="true" type="xs:base64Binary"/>
      <xs:element name="boolean" nillable="true" type="xs:boolean"/>
      <xs:element name="byte" nillable="true" type="xs:byte"/>
      <xs:element name="dateTime" nillable="true" type="xs:dateTime"/>
      <xs:element name="decimal" nillable="true" type="xs:decimal"/>
      <xs:element name="double" nillable="true" type="xs:double"/>
      <xs:element name="float" nillable="true" type="xs:float"/>
      <xs:element name="int" nillable="true" type="xs:int"/>
      <xs:element name="long" nillable="true" type="xs:long"/>
      <xs:element name="QName" nillable="true" type="xs:QName"/>
      <xs:element name="short" nillable="true" type="xs:short"/>
      <xs:element name="string" nillable="true" type="xs:string"/>
      <xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte"/>
      <xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt"/>
      <xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong"/>
      <xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort"/>
      <xs:element name="char" nillable="true" type="tns:char"/>
      <xs:simpleType name="char">
        <xs:restriction base="xs:int"/>
      </xs:simpleType>
      <xs:element name="duration" nillable="true" type="tns:duration"/>
      <xs:simpleType name="duration">
        <xs:restriction base="xs:duration">
          <xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?"/>
          <xs:minInclusive value="-P10675199DT2H48M5.4775808S"/>
          <xs:maxInclusive value="P10675199DT2H48M5.4775807S"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:element name="guid" nillable="true" type="tns:guid"/>
      <xs:simpleType name="guid">
        <xs:restriction base="xs:string">
          <xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:attribute name="FactoryType" type="xs:QName"/>
      <xs:attribute name="Id" type="xs:ID"/>
      <xs:attribute name="Ref" type="xs:IDREF"/>
    </xs:schema>
  </wsdl:types>
  <wsdl:message name="I_Interface_GoodsInfoSync_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsInfoSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_GoodsInfoSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsInfoSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_UnitInfoSync_InputMessage">
    <wsdl:part name="parameters" element="tns:UnitInfoSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_UnitInfoSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:UnitInfoSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_WarehouseInfoSync_InputMessage">
    <wsdl:part name="parameters" element="tns:WarehouseInfoSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_WarehouseInfoSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:WarehouseInfoSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_OrganizationalStructureInfoSync_InputMessage">
    <wsdl:part name="parameters" element="tns:OrganizationalStructureInfoSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_OrganizationalStructureInfoSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:OrganizationalStructureInfoSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_ShelfSpaceSync_InputMessage">
    <wsdl:part name="parameters" element="tns:ShelfSpaceSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_ShelfSpaceSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:ShelfSpaceSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_InboundReceiptSync_InputMessage">
    <wsdl:part name="parameters" element="tns:InboundReceiptSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_InboundReceiptSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:InboundReceiptSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_InventoryReversalReceiptSync_InputMessage">
    <wsdl:part name="parameters" element="tns:InventoryReversalReceiptSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_InventoryReversalReceiptSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:InventoryReversalReceiptSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_OutboundReceiptSync_InputMessage">
    <wsdl:part name="parameters" element="tns:OutboundReceiptSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_OutboundReceiptSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:OutboundReceiptSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_GoodsIssueSync_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsIssueSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_GoodsIssueSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsIssueSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_GoodsIssueReversalReceiptSync_InputMessage">
    <wsdl:part name="parameters" element="tns:GoodsIssueReversalReceiptSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_GoodsIssueReversalReceiptSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:GoodsIssueReversalReceiptSyncResponse"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_InventoryPlanSync_InputMessage">
    <wsdl:part name="parameters" element="tns:InventoryPlanSync"/>
  </wsdl:message>
  <wsdl:message name="I_Interface_InventoryPlanSync_OutputMessage">
    <wsdl:part name="parameters" element="tns:InventoryPlanSyncResponse"/>
  </wsdl:message>
  <wsdl:portType name="I_Interface">
    <wsdl:operation name="GoodsInfoSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/GoodsInfoSync" message="tns:I_Interface_GoodsInfoSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/GoodsInfoSyncResponse" message="tns:I_Interface_GoodsInfoSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="UnitInfoSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/UnitInfoSync" message="tns:I_Interface_UnitInfoSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/UnitInfoSyncResponse" message="tns:I_Interface_UnitInfoSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="WarehouseInfoSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/WarehouseInfoSync" message="tns:I_Interface_WarehouseInfoSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/WarehouseInfoSyncResponse" message="tns:I_Interface_WarehouseInfoSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="OrganizationalStructureInfoSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/OrganizationalStructureInfoSync" message="tns:I_Interface_OrganizationalStructureInfoSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/OrganizationalStructureInfoSyncResponse" message="tns:I_Interface_OrganizationalStructureInfoSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="ShelfSpaceSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/ShelfSpaceSync" message="tns:I_Interface_ShelfSpaceSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/ShelfSpaceSyncResponse" message="tns:I_Interface_ShelfSpaceSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="InboundReceiptSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/InboundReceiptSync" message="tns:I_Interface_InboundReceiptSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/InboundReceiptSyncResponse" message="tns:I_Interface_InboundReceiptSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="InventoryReversalReceiptSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/InventoryReversalReceiptSync" message="tns:I_Interface_InventoryReversalReceiptSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/InventoryReversalReceiptSyncResponse" message="tns:I_Interface_InventoryReversalReceiptSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="OutboundReceiptSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/OutboundReceiptSync" message="tns:I_Interface_OutboundReceiptSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/OutboundReceiptSyncResponse" message="tns:I_Interface_OutboundReceiptSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="GoodsIssueSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/GoodsIssueSync" message="tns:I_Interface_GoodsIssueSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/GoodsIssueSyncResponse" message="tns:I_Interface_GoodsIssueSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="GoodsIssueReversalReceiptSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/GoodsIssueReversalReceiptSync" message="tns:I_Interface_GoodsIssueReversalReceiptSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/GoodsIssueReversalReceiptSyncResponse" message="tns:I_Interface_GoodsIssueReversalReceiptSync_OutputMessage"/>
    </wsdl:operation>
    <wsdl:operation name="InventoryPlanSync">
      <wsdl:input wsaw:Action="http://tempuri.org/I_Interface/InventoryPlanSync" message="tns:I_Interface_InventoryPlanSync_InputMessage"/>
      <wsdl:output wsaw:Action="http://tempuri.org/I_Interface/InventoryPlanSyncResponse" message="tns:I_Interface_InventoryPlanSync_OutputMessage"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_I_Interface" type="tns:I_Interface">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="GoodsInfoSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/GoodsInfoSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UnitInfoSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/UnitInfoSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="WarehouseInfoSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/WarehouseInfoSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OrganizationalStructureInfoSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/OrganizationalStructureInfoSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ShelfSpaceSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/ShelfSpaceSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InboundReceiptSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/InboundReceiptSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InventoryReversalReceiptSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/InventoryReversalReceiptSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="OutboundReceiptSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/OutboundReceiptSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsIssueSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/GoodsIssueSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GoodsIssueReversalReceiptSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/GoodsIssueReversalReceiptSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="InventoryPlanSync">
      <soap:operation soapAction="http://tempuri.org/I_Interface/InventoryPlanSync" style="document"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="S_Interface">
    <wsdl:port name="BasicHttpBinding_I_Interface" binding="tns:BasicHttpBinding_I_Interface">
      <soap:address location="http://127.0.0.1:8001/Service/Interface"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>]]></con:content><con:type>http://schemas.xmlsoap.org/wsdl/</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint></con:endpoints><con:operation id="46d6630b-d841-4587-b3b9-e6a8b56e507b" isOneWay="false" action="http://tempuri.org/I_Interface/GoodsInfoSync" name="GoodsInfoSync" bindingOperationName="GoodsInfoSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="5845af10-f41e-47b5-bebf-9a82096bbe37" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:GoodsInfoSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
[
    {
        "goodsCode": "BATCH001",
        "name": "批量测试物料001",
        "goodsStatus": 1,
        "unitName": "个",
        "goodsVersion": "V1.0",
        "brandVOs": [
            {
                "brandName": "品牌A",
                "purchasesNum": "1",
                "inboundQuantity": 50
            }
        ]
    },
    {
        "goodsCode": "BATCH002",
        "name": "批量测试物料002",
        "goodsStatus": 0,
        "unitName": "套",
        "goodsVersion": "V2.0",
        "brandVOs": [
            {
                "brandName": "品牌B",
                "purchasesNum": "2",
                "inboundQuantity": 75
            }
        ]
    }
]
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:GoodsInfoSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/GoodsInfoSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="e396b7a4-4547-4b72-bb14-db97c00bfe97" isOneWay="false" action="http://tempuri.org/I_Interface/GoodsIssueReversalReceiptSync" name="GoodsIssueReversalReceiptSync" bindingOperationName="GoodsIssueReversalReceiptSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="55a5d6ac-ace0-4f6e-87bc-054a67ef2b83" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:GoodsIssueReversalReceiptSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "outGoodsCodeExport": "outGoodsCodeExport_bcd1adea17b3",
  "warehouseForeman": "warehouseForeman_0ccfc703c0a2",
  "warehouseForemanDate": "2025-07-16 17:56:28",
  "exportBillCode": "exportBillCode_b16385140867",
  "outGoodsDate": "2025-07-16",
  "reason": "reason_7141f2100a6b",
  "warehouseName": "warehouseName_d8d0c85a2165",
  "warehouseId": "warehouseId_e42cfa7d79b1",
  "operatorName": "operatorName_f342f686bf55",
  "operatorId": "operatorId_e049b8f83bd1",
  "outOperatorName": "outOperatorName_e4edf5d2074b",
  "outOperatorId": "outOperatorId_6d98ab38e620",
  "outGoodsCode": "outGoodsCode_4f9103254281",
  "outGoodsName": "outGoodsName_a0018739530c",
  "money": 0.00,
  "taxMoney": 0.00,
  "redOutName": "redOutName_129185b0d2dc",
  "redOutDetailList": [
    {
      "remark": "remark_751f6635e605",
      "goodsId": "goodsId_ea1b47c48750",
      "forWay": "forWay_babc6baa3c5b",
      "forWayId": "forWayId_fb22f169be6c",
      "reason": "reason_be1188c714b3",
      "orderIndex": "orderIndex_2624527eebf2",
      "goodsNum": 0,
      "tax": 0.00,
      "redOutId": "redOutId_33ad829f6f1c",
      "outGoodsInfoId": "outGoodsInfoId_221e3f9081db",
      "localSend": "localSend_8a507bef55ef",
      "gkDeptName": "gkDeptName_e51ab4b32418",
      "gkDeptId": "gkDeptId_611229c39d55",
      "manageDeptName": "manageDeptName_7b2091edca69",
      "manageDeptId": "manageDeptId_eb3b1312e2a3",
      "batch": "batch_8b080c5ae005",
      "gbName": "gbName_156ac88e7079",
      "gbId": "gbId_b2e88bf52327",
      "bzName": "bzName_fe9db5edd44a",
      "bzId": "bzId_c4db5ccc3d50",
      "deptName": "deptName_91a0110f4f9b",
      "deptId": "deptId_d0d4f005a284",
      "orgName": "orgName_2bfaa05dc95f",
      "orgId": "orgId_3f439ab2a3e7",
      "zbCycle": "zbCycle_e0f0942e9de8",
      "produceDate": "2025-07-16",
      "shelfName": "shelfName_b6a3e3e170a1",
      "shelfId": "shelfId_6d8044cef3ab",
      "warehouseName": "warehouseName_1805395c6e31",
      "warehouseId": "warehouseId_a5e7163032e6",
      "taxAllPrice": 0.00,
      "taxPrice": 0.00,
      "brand": "brand_a746d6a9446a",
      "unitId": "unitId_5bf777f857a5",
      "unitName": "unitName_843c1005ebf7",
      "outStorageNum": 0,
      "goodsVersion": "goodsVersion_ab03730475b4",
      "goodsType": "goodsType_b6fe22d400c5",
      "goodsName": "goodsName_df2e4d35ad43",
      "goodsCode": "goodsCode_f8c59bd947c1",
      "outStorageType": "outStorageType_c4955b12044d",
      "createDate": "2025-07-16 17:56:28",
      "createUser": "createUser_71d17b2f7702",
      "createName": "createName_42baa50086fc",
      "updateDate": "2025-07-16 17:56:28",
      "updateUser": "updateUser_19cb229d6df1",
      "updateName": "updateName_ed2516548485",
      "id": "id_6015d6dd3a4e",
      "status": 0,
      "billCode": "billCode_eb0c08510425"
    }
  ],
  "createDate": "2025-07-16 17:56:28",
  "createUser": "createUser_64514c8bb24d",
  "createName": "createName_e68eb5475e9f",
  "updateDate": "2025-07-16 17:56:28",
  "updateUser": "updateUser_7aafb9c33036",
  "updateName": "updateName_cb76233cd7d1",
  "id": "id_6f9498914064",
  "status": 0,
  "billCode": "billCode_e4b6f7b8742b"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:GoodsIssueReversalReceiptSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/GoodsIssueReversalReceiptSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="ae05ae51-a5f5-4c8e-8682-29b3a4b6cb93" isOneWay="false" action="http://tempuri.org/I_Interface/GoodsIssueSync" name="GoodsIssueSync" bindingOperationName="GoodsIssueSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="86465549-81dd-4a83-a304-eeb74c7d1d67" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:GoodsIssueSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "warehouseForeman": "warehouseForeman_978aaab65e0b",
  "warehouseForemanDate": "2025-07-16 17:53:12",
  "exportBillCode": "exportBillCode_868857d1c940",
  "materialRoomWarehouseId": "materialRoomWarehouseId_3f92ff5c3e15",
  "materialRoomWarehouseName": "materialRoomWarehouseName_17415a696f93",
  "pickingType": 0,
  "isToMaterialRoom": 0,
  "deliverTime": "2025-07-16 17:53:12",
  "name": "name_72707d36aa74",
  "described": "described_dcae16caab1f",
  "closeDate": "2025-07-16",
  "workOrderCode": "workOrderCode_a607121bc120",
  "workOrderId": "workOrderId_166d0094f04c",
  "applyType": 0,
  "applyStatus": 0,
  "changeDate": "2025-07-16",
  "applyDate": "2025-07-16",
  "deliverStatus": 0,
  "deliverPhone": "deliverPhone_98c9a080f72a",
  "deliverPeople": "deliverPeople_7d6682cb3c33",
  "deliverAddress": "deliverAddress_b2d937b18ccf",
  "isDeliver": 0,
  "warehouse_MANAGE": "warehouse_MANAGE_cbd13447c4f2",
  "manageDeptId": "manageDeptId_05c936c67acd",
  "manageDeptName": "manageDeptName_a82a3506ffef",
  "applyDeptId": "applyDeptId_0f994f917636",
  "applyDeptName": "applyDeptName_eeeccd0d32a9",
  "applyUserId": "applyUserId_b36912e33a05",
  "applyUserName": "applyUserName_b811bdfd503c",
  "applyGoodsCode": "applyGoodsCode_714e61948951",
  "applyGoodsInfoList": [
    {
      "remark": "remark_d390daf571ee",
      "orderIndex": "orderIndex_679bc490ee5a",
      "canUseNum": 0,
      "mgDept": "mgDept_eafbf43ebecd",
      "mgDeptId": "mgDeptId_e18c623dc2d5",
      "materialRoomWarehouseId": "materialRoomWarehouseId_27647f9b382f",
      "materialRoomWarehouseName": "materialRoomWarehouseName_5f64f15bb4e7",
      "goodsSource": "goodsSource_724d0d5477d6",
      "gkDeptName": "gkDeptName_01926e042ce8",
      "gkDeptId": "gkDeptId_dc3f3130cd01",
      "shelfCode": "shelfCode_69c977b0797e",
      "shelfName": "shelfName_5f838bd56e2d",
      "applyGoodsId": "applyGoodsId_67b549fb93db",
      "describe": "describe_b101699770a6",
      "workOrderId": "workOrderId_c6afb21f108b",
      "isCollect": "isCollect_8aa1dbfdd17e",
      "repairProcess": "repairProcess_56d1e99677b6",
      "isNeedWordOrder": "isNeedWordOrder_1d4800145b6e",
      "major": "major_07c729627651",
      "projectInfo": "projectInfo_ec6bd5c15d0b",
      "goodsVersion": "goodsVersion_f220feca500d",
      "useForBuffer": "useForBuffer_6bf4063a6fec",
      "useFor": "useFor_126d22b8869d",
      "num": 0,
      "lineId": "lineId_abad357b5692",
      "lineName": "lineName_9b534f477963",
      "forLineName": "forLineName_d9671eff4137",
      "forLineId": "forLineId_7e4955c40777",
      "buyType": "buyType_0d86462bda4a",
      "shelfId": "shelfId_f354a4e6cfa9",
      "warehouseId": "warehouseId_e8845deb72ca",
      "warehouseName": "warehouseName_4188d22634fe",
      "unitId": "unitId_d435d3f65f75",
      "unitName": "unitName_68dd2b49a31f",
      "brand": "brand_b031add0786d",
      "goodsAttribute": "goodsAttribute_e4363813316b",
      "goodsType": "goodsType_d1d01df96b3a",
      "goodsName": "goodsName_e41b51a917bb",
      "goodsCode": "goodsCode_eef2f1122ffa",
      "inventoryId": "inventoryId_5e2f60528c30",
      "goodsId": "goodsId_7ed5c5cb3afe",
      "storageInfoId": "storageInfoId_5eec8a03d494",
      "actualNum": 0, 
      "createDate": "2025-07-16 17:53:12",
      "createUser": "createUser_26d8a1a4b639",
      "createName": "createName_2aa4838d064b",
      "updateDate": "2025-07-16 17:53:12",
      "updateUser": "updateUser_86513f48387c",
      "updateName": "updateName_5f311fbfe4fa",
      "id": "id_95182c3e1413",
      "status": 0,
      "billCode": "billCode_d9e247ea43e7"
    }
  ],
  "createDate": "2025-07-16 17:53:12",
  "createUser": "createUser_a3ea025a216e",
  "createName": "createName_75c56a3635c7",
  "updateDate": "2025-07-16 17:53:12",
  "updateUser": "updateUser_2f33e5163879",
  "updateName": "updateName_4c5dfb1892a9",
  "id": "id_a5de5fa256ac",
  "status": 0,
  "billCode": "billCode_fdd00bc310ba"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:GoodsIssueSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/GoodsIssueSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="1b49c8e2-777b-45a2-b68b-4acb88d2a512" isOneWay="false" action="http://tempuri.org/I_Interface/InboundReceiptSync" name="InboundReceiptSync" bindingOperationName="InboundReceiptSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="0289aa2c-52b6-479c-9f74-c67b2148e120" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:InboundReceiptSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "stockTakeResultId": "stockTakeResultId_8e6a369008f9",
  "stockTakeResultCode": "stockTakeResultCode_da6e57e5f86d",
  "stockTakeResultName": "stockTakeResultName_4846e48b1a82",
  "warehouseForeman": "warehouseForeman_83ed654f79dd",
  "warehouseForemanDate": "2025-07-16 17:40:09",
  "exportBillCode": "exportBillCode_67ad199fd9a6",
  "exportCheckedCode": "exportCheckedCode_a9fb78c61020",
  "isRedStorage": 0,
  "money": 0.00,
  "taxMoney": 0.00,
  "storageStatus": "storageStatus_26f0e9ed7067",
  "storageGoodsSource": "storageGoodsSource_438f0b0711a6",
  "storageDate": "2025-07-16",
  "warehouseId": "warehouseId_977dbba4f97c",
  "warehouseName": "warehouseName_20a2502ce3e3",
  "storageCode": "storageCode_01",
  "storageName": "storageName_42d29550ebec",
  "contractId": "contractId_12f9ed550c4e",
  "contractCode": "contractCode_ffc7354d8561",
  "contractName": "contractName_f5d7fb16cb78",
  "supplierId": "supplierId_f7e932c1c46d",
  "supplier": "supplier_4ed3640cff04",
  "supplierPhone": "supplierPhone_2637651a32f6",
  "buyOrderId": "buyOrderId_18f5d15b5a40",
  "buyOrderCode": "buyOrderCode_88b38e047db1",
  "buyOrderName": "buyOrderName_c95b28541f5a",
  "buyOrderOperatorId": "buyOrderOperatorId_9311e3472e4d",
  "buyOrderOperator": "buyOrderOperator_6d65b1f56b1f",
  "secondParty": "secondParty_ab6fbe41cfdb",
  "secondPartyId": "secondPartyId_1c60f95253e0",
  "receiveId": "receiveId_336aad1d630c",
  "receiveCode": "receiveCode_053deabb0ee1",
  "receiveName": "receiveName_6d8f7b4fa9b2",
  "receiveUserId": "receiveUserId_6615213c7d15",
  "receiveUser": "receiveUser_86f90db6f394",
  "deliveryId": "deliveryId_d991f6a6d0ea",
  "deliveryCode": "deliveryCode_9d01c4c590b4",
  "deliveryName": "deliveryName_cf3f6c84f321",
  "checkedId": "checkedId_1aa1041e712d",
  "checkedCode": "checkedCode_bfbdff30881d",
  "checkedName": "checkedName_fe1ec02de295",
  "isPass": 0,
  "storageMoneyTax": 0.00,
  "initId": "initId_42ec40c4778f",
  "storageInfoList": [
    {
      "contractCode": "contractCode_fd69b0084b90",
      "orderIndex": "orderIndex_8f8d1e7ff60d",
      "redStorageAllNum": 0,
      "isControlledByProductDate": "isControlledByProductDate_faf163f84545",
      "deliveryWarehouseId": "deliveryWarehouseId_4cbe1a775ff5",
      "deliveryWarehouse": "deliveryWarehouse_24adc81d2ac8",
      "mgDeptName": "mgDeptName_85973cf42263",
      "mgDeptId": "mgDeptId_49a04a5f155c",
      "storageId": "storageId_023da939f3c6",
      "checkedInfoId": "checkedInfoId_f6d87253b29a",
      "initId": "initId_8449adcd3e94",
      "goodsId": "goodsId_621e071cb29f",
      "goodsCode": "2300490100100401",
      "goodsName": "消防战斗服",
      "goodsType": "goodsType_baa92db6c993",
      "goodsSource": "goodsSource_3cf631081c0f",
      "brand": "brand_9b02c6307289",
      "goodsVersion": "goodsVersion_9b69e3ed9dd8",
      "goodsAttribute": "goodsAttribute_9f4064f19356",
      "needGoodsNum": 0,
      "contractGoodsNum": 0,
      "addGoodsNum": 0,
      "storageNum": 90,
      "unitId": "unitId_d4a50c2eb0cf",
      "unitName": "unitName_b01917a1a3b0",
      "warehouseId": "warehouseId_4dea61dc9a13",
      "warehouseName": "warehouseName_259f8b708557",
      "shelfId": "shelfId_6d70cab76df9",
      "shelfName": "shelfName_bd42d2c780fb",
      "batch": "batch_333ebc81f898",
      "taxPrice": 0.00,
      "taxMoney": 0.00,
      "tax": 0.00,
      "price": 0.00,
      "money": 0.00,
      "orgId": "orgId_99c6e53f44ef",
      "orgName": "orgName_1328333b3b26",
      "deptId": "deptId_6e52b06bccb6",
      "deptName": "deptName_215edff51de7",
      "bzId": "bzId_e992e62917c1",
      "bzName": "bzName_4bf3deecb9d2",
      "gbId": "gbId_3c4cb374c63e",
      "gbName": "gbName_3ed789d71b65",
      "manageDeptId": "manageDeptId_38d33774a206",
      "manageDeptName": "manageDeptName_566984c7b9b6",
      "lineId": "lineId_8af4ff137d30",
      "lineName": "lineName_8dc098ecf085",
      "gkDeptId": "gkDeptId_81d1e0cc26d3",
      "gkDeptName": "gkDeptName_bb940939e303",
      "localSend": "localSend_88eddb3b5deb",
      "remark": "remark_33f100b30be6",
      "createDate": "2025-07-16 17:40:09",
      "createUser": "createUser_0d02da3e22a7",
      "createName": "createName_14b88cd585d3",
      "updateDate": "2025-07-16 17:40:09",
      "updateUser": "updateUser_2181e8c78055",
      "updateName": "updateName_0065282a39ee",
      "id": "id_4596845e99aa",
      "status": 0,
      "billCode": "billCode_3bda9a9c3416"
    }
  ],
  "createDate": "2025-07-16 17:40:09",
  "createUser": "createUser_c84c945f5b3d",
  "createName": "createName_ba8be75d73f8",
  "updateDate": "2025-07-16 17:40:09",
  "updateUser": "updateUser_05e4faa56119",
  "updateName": "updateName_78f7bcdeeeac",
  "id": "id_8b18324f30ef",
  "status": 0,
  "billCode": "billCode_84f65e9ef9d4"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:InboundReceiptSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/InboundReceiptSync"/><con:wsrmConfig version="1.2"/></con:call><con:call id="92b611e9-bbb9-4937-8e52-ae2baf6666bd" name="Copy of Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:InboundReceiptSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{"isRedStorage":0,"money":4100,"taxMoney":3690,"storageGoodsSource":"1","storageDate":"2025-08-22 00:00:00","warehouseId":"b8f0d859699d442e813b1d2caf75d3d7","warehouseName":"立体库","storageCode":"SHCGSQ20250821005-SH-jyrk","storageName":"采购订单20250821-1-送货单-收货单-检验单入库单","contractId":"69f7699f8bcf4881bbf8d4727bd9f026","contractCode":"**********","contractName":"合同20250819","supplierId":"5f228d1ac542480da6420d5afb4e7074","supplier":"徐州林锐百货商贸有限公司","supplierPhone":"17751929952","buyOrderId":"63b6999c93544c048b647e5e22d39215","buyOrderCode":"CGSQ20250821005","buyOrderName":"采购订单20250821-1","buyOrderOperatorId":"0599c9a6a7684cf0a307b83433dab236","buyOrderOperator":"李天戈","secondParty":"徐州林锐百货商贸有限公司","secondPartyId":"5f228d1ac542480da6420d5afb4e7074","receiveId":"f4bfbfbd5077434abc22e6be96bd874d","receiveCode":"SHCGSQ20250821005-SH","receiveName":"采购订单20250821-1-送货单-收货单","receiveUserId":"0599c9a6a7684cf0a307b83433dab236","receiveUser":"李天戈","deliveryId":"44e120eccb074a168180a95a8a212e9c","deliveryCode":"SHCGSQ20250821005","deliveryName":"采购订单20250821-1-送货单","checkedId":"e017019e162442e086bae2299182b142","checkedCode":"SHCGSQ20250821005-SH-jy","checkedName":"采购订单20250821-1-送货单-收货单-检验单","isPass":0,"storageMoneyTax":3690,"initId":"69f7699f8bcf4881bbf8d4727bd9f026","storageInfoList":[{"deleteFlag":0,"mgDeptName":"机电部","mgDeptId":"97300","storageId":"ba692814a4d54a699e7508dd194127c0","checkedInfoId":"290abb09f7b94f2e991f4e89aa130117","initId":"3140290201654d23825c8664b69e13e7","goodsId":"25f4d99026b649f0b40d2f2665bb98ae","goodsCode":"20103040003201","goodsName":"长螺丝刀","goodsType":"01","goodsSource":"1","brand":"世达","goodsVersion":"25CM，5&","needGoodsNum":6,"storageNum":6,"unitName":"个","warehouseId":"b8f0d859699d442e813b1d2caf75d3d7","warehouseName":"立体库","shelfId":"2025082621","shelfName":"01排17列01层","batch":"rkd20250822001","taxPrice":100,"taxMoney":600,"tax":10,"price":90,"money":540,"orgId":"97","orgName":"运营分公司","deptId":"97300","deptName":"机电部","gbId":"97310","gbName":"信号室","manageDeptId":"97","manageDeptName":"运营分公司","lineId":"6bb18f00544042e6ac3128c47ba0cdaf","lineName":"6号线","gkDeptId":"97100","gkDeptName":"综合管理部","entityModelId":"0a545b978e9845c2bc00ee16e0ae9afe","createDate":"2025-08-22 07:54:57","createUser":"1","createName":"超级管理员","updateDate":"2025-08-25 06:22:33","updateUser":"1","updateName":"超级管理员","id":"89fb76f1d3c24553b8df856aa7ed1e52","status":1},{"deleteFlag":0,"mgDeptName":"机电部","mgDeptId":"97300","storageId":"ba692814a4d54a699e7508dd194127c0","checkedInfoId":"1fdd84a6c2f44438b504158e478f786b","initId":"fe8bdf678205407aad35c9c99c63f65a","goodsId":"ff04143825d1488aa2a32e7e359b3f73","goodsCode":"21113042003001","goodsName":"服务器","goodsType":"05","goodsSource":"1","brand":"联想","goodsVersion":"联想F550","needGoodsNum":4,"storageNum":4,"unitName":"台","warehouseId":"b8f0d859699d442e813b1d2caf75d3d7","warehouseName":"立体库","shelfId":"2025082621","shelfName":"01排17列01层","batch":"rkd20250822002","taxPrice":200,"taxMoney":800,"tax":10,"price":180,"money":720,"orgId":"97","orgName":"运营分公司","deptId":"97300","deptName":"机电部","bzId":"97310003","bzName":"信号正线三工班","gbId":"97310","gbName":"信号室","manageDeptId":"97","manageDeptName":"运营分公司","lineId":"6bb18f00544042e6ac3128c47ba0cdaf","lineName":"6号线","gkDeptId":"97100","gkDeptName":"综合管理部","entityModelId":"0a545b978e9845c2bc00ee16e0ae9afe","createDate":"2025-08-22 07:54:57","createUser":"1","createName":"超级管理员","updateDate":"2025-08-25 06:22:33","updateUser":"1","updateName":"超级管理员","id":"ef903df5d7234868b929717e5e088dbc","status":1},{"deleteFlag":0,"mgDeptName":"机电部","mgDeptId":"97300","storageId":"ba692814a4d54a699e7508dd194127c0","checkedInfoId":"61bc9fc88f9b4b808a18a0fba4e7dfd2","initId":"28efb712813641d6b3aeff497cf738c8","goodsId":"fdea251cab5a4ea7805699bb2342d93d","goodsCode":"22123043002701","goodsName":"五孔插座","goodsType":"05","goodsSource":"1","brand":"开心","goodsVersion":"公牛202505","needGoodsNum":9,"storageNum":9,"unitName":"个","warehouseId":"b8f0d859699d442e813b1d2caf75d3d7","warehouseName":"立体库","shelfId":"2025082621","shelfName":"01排17列01层","batch":"rkd20250822003","taxPrice":300,"taxMoney":2700,"tax":10,"price":270,"money":2430,"orgId":"97","orgName":"运营分公司","deptId":"97300","deptName":"机电部","bzId":"97310003","bzName":"信号正线三工班","gbId":"97310","gbName":"信号室","manageDeptId":"97","manageDeptName":"运营分公司","lineId":"6bb18f00544042e6ac3128c47ba0cdaf","lineName":"6号线","gkDeptId":"97100","gkDeptName":"综合管理部","entityModelId":"0a545b978e9845c2bc00ee16e0ae9afe","createDate":"2025-08-22 07:54:57","createUser":"1","createName":"超级管理员","updateDate":"2025-08-25 06:22:33","updateUser":"1","updateName":"超级管理员","id":"5cf42d7287ea4e6cb51f9218a3cabd25","status":1}],"entityModelId":"b0b0672f962d4b4e9cd2cc0506e8a02c","processDefinitionId":"d50b6664-817b-11f0-9f10-fefcfe394de1","processInstanceId":"e2d3feb5-817b-11f0-9f10-fefcfe394de1","processInstanceKey":"process_10-10000-0-1745483663867-0vhjt2co","createDate":"2025-08-22 07:54:57","createUser":"1","createName":"超级管理员","updateDate":"2025-08-26 01:57:19","updateUser":"1","updateName":"超级管理员","id":"ba692814a4d54a699e7508dd194127c0","status":41,"billCode":"RKSQ20250822002"}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:InboundReceiptSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/InboundReceiptSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="c9e4a1ff-3026-48fe-8e0d-29815036f7fb" isOneWay="false" action="http://tempuri.org/I_Interface/InventoryPlanSync" name="InventoryPlanSync" bindingOperationName="InventoryPlanSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="8cd90d99-5106-450b-99b6-e034a177e8a9" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:InventoryPlanSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "stockTakeName": "stockTakeName_90cc5c8c2b2f",
  "stockTakeCode": "stockTakeCode_5d11a76e5e6e",
  "description": "description_8e9d9f2abff8",
  "stockTakeType": "stockTakeType_aa3e14460c33",
  "stockTakeWay": "stockTakeWay_4ab867e36490",
  "stockTakeStartDate": "2025-07-17",
  "stockTakeEndDate": "2025-07-17",
  "unitId": "unitId_14a8a21a1f24",
  "unitName": "unitName_a1109768071e",
  "operatorId": "operatorId_8abcf1d25b16",
  "operatorName": "operatorName_92fc478f6d06",
  "stockTakeManageId": "stockTakeManageId_d0d7b062378b",
  "stockTakeManageName": "stockTakeManageName_ebcf1a02f2af",
  "stockTakeWarehouseList": [
    {
      "stockTakeId": "stockTakeId_be71606a64f2",
      "warehouseId": "warehouseId_6f3c11be0303",
      "warehouseCode": "warehouseCode_ef07a5877a93",
      "warehouseName": "warehouseName_c28896a97e70",
      "highGoods": "highGoods_c6905b047401",
      "stockTakeRatio": "stockTakeRatio_54b608de2541",
      "shelfId": "shelfId_9ecdb64e710b",
      "shelfName": "shelfName_746b924ee34c",
      "createDate": "2025-07-17 09:04:01",
      "createUser": "createUser_92c36bf9ba38",
      "createName": "createName_b9aaf3dc35d0",
      "updateDate": "2025-07-17 09:04:01",
      "updateUser": "updateUser_1df93220a38d",
      "updateName": "updateName_d3b6fcd942ab",
      "id": "id_80c2e631b0a0",
      "status": 0,
      "billCode": "billCode_65f0be97061e"
    }
  ],
  "stockTakeGoodsList": [
    {
      "inventoryId": "inventoryId_37cbb399de0f",
      "stockTakeId": "stockTakeId_afba08394369",
      "goodsId": "goodsId_3ba541ec500c",
      "goodsCode": "2800190101100101",
      "goodsName": "工业无纺清洁布",
      "goodsVersion": "goodsVersion_c01e73e28a42",
      "storageNum": 1000,
      "unitId": "unitId_43e957ca7f2f",
      "unitName": "unitName_758649c3b12d",
      "brand": "brand_520d210847e2",
      "warehouseId": "warehouseId_f8e2584615d3",
      "warehouseName": "warehouseName_df3d3237104f",
      "warehouseCode": "warehouseCode_17a10f1804ff",
      "shelfId": "shelfId_f281b7c989cd",
      "shelfName": "shelfName_e0e294f94379",
      "shelfCode": "shelfCode_5446fe9bd57c",
      "stockTakeUserId": "stockTakeUserId_b4dcf14bea6b",
      "stockTakeUserName": "stockTakeUserName_d0733c844ac1",
      "stockTakePlanStartDate": "2025-07-17",
      "stockTakePlanEndDate": "2025-07-17",
      "createDate": "2025-07-17 09:04:01",
      "createUser": "createUser_80034b043c49",
      "createName": "createName_70a2d3b40104",
      "updateDate": "2025-07-17 09:04:01",
      "updateUser": "updateUser_7029390c8040",
      "updateName": "updateName_f1789e4b5ad9",
      "id": "id_97ac9cf980a1",
      "status": 0,
      "billCode": "billCode_26a4cc5b4b53"
    }
  ],
  "stockTakeMangeDeptId": "stockTakeMangeDeptId_38c364e4b925",
  "stockTakeMangeDeptName": "stockTakeMangeDeptName_9d1083db4cb0",
  "createDate": "2025-07-17 09:04:01",
  "createUser": "createUser_2b626ba2ae79",
  "createName": "createName_5120f62594b2",
  "updateDate": "2025-07-17 09:04:01",
  "updateUser": "updateUser_aea0fa5b7957",
  "updateName": "updateName_7a4dcea7d7ff",
  "id": "id_45af294b75c7",
  "status": 0,
  "billCode": "billCode_2b7e53ed548e"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:InventoryPlanSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/InventoryPlanSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="d3da9bc3-7d6b-4e31-8137-83257126fb59" isOneWay="false" action="http://tempuri.org/I_Interface/InventoryReversalReceiptSync" name="InventoryReversalReceiptSync" bindingOperationName="InventoryReversalReceiptSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="2631b4c7-b5b5-4cbf-91bc-e646816e8698" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:InventoryReversalReceiptSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "redStorageType": 1,
  "inGoodsCodeExport": "inGoodsCodeExport_88a747a8a202",
  "materialManagementEngineer": "materialManagementEngineer_508bdac2067d",
  "materialManagementEngineerDate": "2025-07-16 17:46:00",
  "exportBillCode": "exportBillCode_4ee33cb8dfdc",
  "initId": "initId_1c86a9c4dee7",
  "buyOrderCode": "buyOrderCode_35a46061689c",
  "contractCode": "contractCode_ca57364608f7",
  "reason": "reason_f2d3d94ce7bc",
  "warehouseName": "warehouseName_d72458c7a2a6",
  "warehouseId": "warehouseId_8d81aeb3b87d",
  "storageDate": "2025-07-16 17:46:00",
  "sourceUserName": "sourceUserName_4ee52f61a685",
  "sourceUserId": "sourceUserId_8d355bb7859d",
  "sourceUnitName": "sourceUnitName_c82125e69bc8",
  "sourceUnitId": "sourceUnitId_42065721ef06",
  "operatorName": "operatorName_063850af7e5c",
  "operatorId": "operatorId_44b52de2ee15",
  "storageOperatorName": "storageOperatorName_01a6525214a6",
  "storageOperatorId": "storageOperatorId_87a4bf6208c2",
  "storageCode": "storageCode_889a168272bd",
  "storageName": "storageName_39d760bf36b7",
  "storageGoodsSource": "storageGoodsSource_e5cd5054754e",
  "money": 0.00,
  "taxMoney": 0.00,
  "redStorageName": "redStorageName_03f37159360f",
  "redStorageDetailList": [
    {
      "reason": "reason_c319eebe50ea",
      "lineId": "lineId_b00f33b98d0d",
      "lineName": "lineName_b54d678a1875",
      "orderIndex": "orderIndex_76eb10b901e6",
      "initId": "initId_f9091c012c27",
      "tax": 0.00,
      "goodsType": "goodsType_4cad8d0f9c53",
      "goodsVersion": "goodsVersion_166456a0f671",
      "redStorageId": "redStorageId_774c62813822",
      "remark": "remark_91a8a80adeb7",
      "localSend": "localSend_79d5fbc5de42",
      "gkDeptName": "gkDeptName_5bf1aad8ef5e",
      "gkDeptId": "gkDeptId_780ef4f13ea0",
      "manageDeptName": "manageDeptName_b5fa868f3f92",
      "manageDeptId": "manageDeptId_3c8d1d02f493",
      "batch": "batch_a30f760ad607",
      "gbName": "gbName_960c6e2da626",
      "gbId": "gbId_045d2615802e",
      "bzName": "bzName_c2827e0f394f",
      "bzId": "bzId_dc2c37ece89d",
      "deptName": "deptName_0df3b8987cc2",
      "deptId": "deptId_725476b662d3",
      "orgName": "orgName_c51f2cef97e4",
      "orgId": "orgId_6dac965fceff",
      "zbCycle": "zbCycle_4055e46f2bcc",
      "produceDate": "2025-07-16",
      "shelfName": "shelfName_a10ee183e372",
      "shelfId": "shelfId_8807aafea879",
      "warehouseName": "warehouseName_20673f4850a9",
      "warehouseId": "warehouseId_c4786b746252",
      "taxAllPrice": 0.00,
      "taxPrice": 0.00,
      "brand": "brand_27340b168723",
      "unitName": "unitName_2b45d3aff434",
      "unitId": "unitId_8a90c35ee849",
      "redStorageNum": 0,
      "storageNum": 0,
      "goodsName": "goodsName_08a66b797063",
      "goodsCode": "goodsCode_053edcc2f3b0",
      "goodsId": "goodsId_20e906977b06",
      "storageType": "storageType_69ab4444e9d2",
      "sourceType": "sourceType_f01efcae383f",
      "entityModelId": "entityModelId_735f5c4c39d4",
      "processDefinitionId": "processDefinitionId_33377c61003b",
      "processInstanceId": "processInstanceId_44d66a079ee5",
      "processInstanceKey": "processInstanceKey_ba43775b66f5",
      "nextProcessUserList": {},
      "attachmentFileName": "attachmentFileName_f66b4f4ba955",
      "attachmentFileUrl": "attachmentFileUrl_3a044a6bacc3",
      "createDate": "2025-07-16 17:46:00",
      "createUser": "createUser_175cd30e49dd",
      "createName": "createName_e4c4761f20d1",
      "updateDate": "2025-07-16 17:46:00",
      "updateUser": "updateUser_74421ebcaf11",
      "updateName": "updateName_db003c4a491e",
      "id": "id_a1a91cefe658",
      "status": 0,
      "billCode": "billCode_8dfbd8d62e47"
    }
  ],
  "sourceType": "sourceType_0ed1dfc1a1af",
  "entityModelId": "entityModelId_3a0e4e386b04",
  "processDefinitionId": "processDefinitionId_5af86cdf7790",
  "processInstanceId": "processInstanceId_177b8b16e572",
  "processInstanceKey": "processInstanceKey_581694c0e776",
  "nextProcessUserList": {},
  "attachmentFileName": "attachmentFileName_ed1e09239c97",
  "attachmentFileUrl": "attachmentFileUrl_82ef0a548a81",
  "createDate": "2025-07-16 17:46:00",
  "createUser": "createUser_0474ac3c4499",
  "createName": "createName_eeaa1b78b3e5",
  "updateDate": "2025-07-16 17:46:00",
  "updateUser": "updateUser_a1ba51a15018",
  "updateName": "updateName_4d9095e28fd7",
  "id": "id_68f8449fc9cd",
  "status": 0,
  "billCode": "billCode_8dd09d22d90d"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:InventoryReversalReceiptSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/InventoryReversalReceiptSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="31b21201-246f-4c93-8ebc-59ddb2bc67c7" isOneWay="false" action="http://tempuri.org/I_Interface/OrganizationalStructureInfoSync" name="OrganizationalStructureInfoSync" bindingOperationName="OrganizationalStructureInfoSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="476640a0-30dd-4d64-99df-9b99a8c24d25" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:OrganizationalStructureInfoSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "orgLevel": 0,
  "orgCode": "orgCode_75ff23e01fab",
  "orgName": "orgName_350f1f11f122",
  "parentOrgCode": "parentOrgCode_d69c12eca96b",
  "oldOrgCode": "oldOrgCode_6a66fe8de6e4",
  "orgStatus": 0,
  "isDelete": 0,
  "orgType": "orgType_ed284c49194e",
  "personInCharge": "personInCharge_ad957374385c",
  "type": 0,
  "code": "code_648565b7b595",
  "createDate": "2025-07-16 17:33:45",
  "updateDate": "2025-07-16 17:33:45",
  "id": "id_0d80ce1c3dc3"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:OrganizationalStructureInfoSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/OrganizationalStructureInfoSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="cd412fec-0cc7-46df-af77-d978241f6308" isOneWay="false" action="http://tempuri.org/I_Interface/OutboundReceiptSync" name="OutboundReceiptSync" bindingOperationName="OutboundReceiptSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="191812ec-01bd-4360-b1a9-b9fff291908f" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:OutboundReceiptSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "deliverAddress": "deliverAddress_4425b9dcddb8",
  "isDeliver": 0,
  "exportBillCode": "exportBillCode_392cb72accc8",
  "outboundType": 1,
  "applyType": 0,
  "isRedOut": 0,
  "chargeUser": "chargeUser_0018b7e0b3fa",
  "chargeUserId": "chargeUserId_473a2c5fd83e",
  "deliverPhone": "deliverPhone_81c037a1fdbd",
  "deliverPeople": "deliverPeople_b0b3226c00d3",
  "deliverTime": "2025-07-16 17:49:10",
  "pickingType": 0,
  "materialRoomWarehouseId": "materialRoomWarehouseId_b5231fdddbc6",
  "materialRoomWarehouseName": "materialRoomWarehouseName_aa2d523722db",
  "isPass": 0,
  "outGoodsName": "outGoodsName_f7afbbfa261c",
  "operatorId": "operatorId_4734f30d322e",
  "operatorName": "operatorName_0b9e1fec53f2",
  "operatTime": "2025-07-16",
  "describe": "describe_a150ae0677af",
  "closeDate": "2025-07-16",
  "applyUserName": "applyUserName_3cc6490cc471",
  "applyUserId": "applyUserId_dd255fa5a98a",
  "applyDeptName": "applyDeptName_49e5489785b8",
  "applyDeptId": "applyDeptId_465073085f2e",
  "workCode": "workCode_6c0985676513",
  "applyDate": "2025-07-16",
  "applyCode": "applyCode_05f44c63a29e",
  "outGoodsInfoList": [
    {
      "remark": "remark_d1e8d1ce2704",
      "applyUserName": "applyUserName_5959e58d3489",
      "orderIndex": "orderIndex_e75dd1c18d23",
      "applyGoodsId": "applyGoodsId_de20654a10ed",
      "applyGoodsInfoId": "applyGoodsInfoId_6a25568ff904",
      "canUseNum": 0,
      "redOutAllNum": 0,
      "inventoryId": "inventoryId_c7409da503d5",
      "materialRoomWarehouseId": "materialRoomWarehouseId_ce94bd4ae9f0",
      "materialRoomWarehouseName": "materialRoomWarehouseName_9a1dbd654c9d",
      "outGoodsId": "outGoodsId_0f4a06a972f5",
      "isNeedWordOrder": 0,
      "repairProcess": "repairProcess_314995fc1b44",
      "major": "major_aa1f4d385ddd",
      "forWay": "forWay_956e4b70c0c1",
      "forWayId": "forWayId_6158330aa5ce",
      "projectInfo": "projectInfo_4905f30eafcd",
      "useForBuffer": "useForBuffer_cce1da62545e",
      "useFor": "useFor_4063f3107c20",
      "goodsNum": 10,
      "mgDeptName": "mgDeptName_47fda85095dd",
      "mgDeptId": "mgDeptId_3ec712e82625",
      "warehouseWay": "warehouseWay_8695b0029d63",
      "warehouseWayId": "warehouseWayId_eaa8e440195e",
      "shelfName": "shelfName_67b1d3d4e252",
      "shelfId": "shelfId_e9902afe9338",
      "warehouseName": "warehouseName_cffe40af0f55",
      "warehouseId": "warehouseId_dcfccac56417",
      "goodsType": "goodsType_34b03500cfa5",
      "goodsAttribute": "goodsAttribute_eeea516117b6",
      "brand": "brand_119683851f86",
      "unitId": "unitId_8bc754efb1a0",
      "unitName": "unitName_4d7cc992afbe",
      "goodsVersion": "goodsVersion_86a8f31a7994",
      "goodsName": "goodsName_c87004a545d7",
      "goodsCode": "2399999900201401",
      "goodsId": "goodsId_b9671ed5a8fb",
      "sourceType": "sourceType_c6529ad9421d",
      "entityModelId": "entityModelId_813df2a896eb",
      "processDefinitionId": "processDefinitionId_dca7c830757f",
      "processInstanceId": "processInstanceId_e7e335af441d",
      "processInstanceKey": "processInstanceKey_d5e67007e315",
      "nextProcessUserList": {},
      "attachmentFileName": "attachmentFileName_bd217619d4f7",
      "attachmentFileUrl": "attachmentFileUrl_2a4663126b59",
      "createDate": "2025-07-16 17:49:10",
      "createUser": "createUser_ab737c6879af",
      "createName": "createName_c50d9f7d2c0e",
      "updateDate": "2025-07-16 17:49:10",
      "updateUser": "updateUser_f923eef1f5ba",
      "updateName": "updateName_43df603d199f",
      "id": "id_0a3fafb215d5",
      "status": 0,
      "billCode": "billCode_abe21f3c6956"
    }
  ],
  "sourceType": "sourceType_ae3022340a98",
  "entityModelId": "entityModelId_cf3572df83fc",
  "processDefinitionId": "processDefinitionId_11a8b6aecb0b",
  "processInstanceId": "processInstanceId_f60ae4ebbb35",
  "processInstanceKey": "processInstanceKey_50bb871b5519",
  "nextProcessUserList": {},
  "attachmentFileName": "attachmentFileName_2badf8508802",
  "attachmentFileUrl": "attachmentFileUrl_4ecca6cc5376",
  "createDate": "2025-07-16 17:49:10",
  "createUser": "createUser_4dea48d74f43",
  "createName": "createName_4929043d362d",
  "updateDate": "2025-07-16 17:49:10",
  "updateUser": "updateUser_845f869a2fd4",
  "updateName": "updateName_d1fe10317ef6",
  "id": "id_0a0a5fb2cfca",
  "status": 0,
  "billCode": "billCode_09d7e1eb9a7b"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:OutboundReceiptSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/OutboundReceiptSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="1b71de65-dc1b-4df0-bdea-732f86af1b2f" isOneWay="false" action="http://tempuri.org/I_Interface/ShelfSpaceSync" name="ShelfSpaceSync" bindingOperationName="ShelfSpaceSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="25efa403-8b7b-47e7-8089-09019c6837db" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:ShelfSpaceSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "isHaveMaterials": 0,
  "belongingPlace": "belongingPlace_9c833c74df75",
  "gsId": "gsId_e49379c254a3",
  "gsName": "gsName_8874b6979ef1",
  "lineId": "lineId_eff1a6501a9a",
  "lineName": "lineName_e88bcbc36561",
  "remark": "remark_7862b42e018f",
  "addressType": "addressType_6a6eedc375fc",
  "hasGoods": false,
  "warehouseCharge": "warehouseCharge_84a880e2c4fe",
  "warehouseChargeName": "warehouseChargeName_eafbb736e19c",
  "warehouseAddress": "warehouseAddress_4972a3f130f7",
  "warehouseType": "warehouseType_1d2031263c53",
  "qrCode": "qrCode_8b8344c40eb2",
  "shelfStatus": 0,
  "describe": "describe_f013e9edd725",
  "warehouseCode": "warehouseCode_989eea826a9c",
  "warehouseName": "warehouseName_f13a3da79bd6",
  "warehouseId": "warehouseId_d213d3b02eea",
  "name": "name_8386b583dff1",
  "code": "code_04fc92e0fad9",
  "sourceType": "sourceType_7d996efcabe1",
  "entityModelId": "entityModelId_09255fd88d92",
  "processDefinitionId": "processDefinitionId_d258e9425af9",
  "processInstanceId": "processInstanceId_e45409350930",
  "processInstanceKey": "processInstanceKey_29d72e8eaf38",
  "nextProcessUserList": {},
  "attachmentFileName": "attachmentFileName_018bbddf8723",
  "attachmentFileUrl": "attachmentFileUrl_2caf54cb7906",
  "createDate": "2025-07-16 17:35:45",
  "createUser": "createUser_98b30f94f9cb",
  "createName": "createName_e8946bee0185",
  "updateDate": "2025-07-16 17:35:45",
  "updateUser": "updateUser_b0867f1cb51c",
  "updateName": "updateName_3ec213015f58",
  "id": "id_db768f351a11",
  "status": 0,
  "billCode": "billCode_b00512735873"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:ShelfSpaceSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/ShelfSpaceSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="8edfa702-13fc-490e-95b2-e2a5effc01d3" isOneWay="false" action="http://tempuri.org/I_Interface/UnitInfoSync" name="UnitInfoSync" bindingOperationName="UnitInfoSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="d771274b-a450-4531-8b73-c926c1b26575" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:UnitInfoSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[{
  "measuringUnitStatus": "measuringUnitStatus_9c9ccd2de1da",
  "measuringUnitCode": "measuringUnitCode_7e761420a544",
  "code": "code_9107b8504dae",
  "parentId": "parentId_e5982edb4583",
  "name": "name_62691684d5fa",
  "ignoreParent": false,
  "createDate": "2025-07-16 16:48:18",
  "createUser": "createUser_1c43794770b9",
  "createName": "createName_53692366e154",
  "updateDate": "2025-07-16 16:48:18",
  "updateUser": "updateUser_2d7317c7714a",
  "updateName": "updateName_91726c9f4019",
  "id": "id_768f8804d4b1",
  "status": 0,
  "billCode": "billCode_8dbb966c514e"
}]]]]>><![CDATA[
</tem:inputJson>\r
      </tem:UnitInfoSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/UnitInfoSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="e32a2912-4e4c-432a-a536-e72b3be6cf48" isOneWay="false" action="http://tempuri.org/I_Interface/WarehouseInfoSync" name="WarehouseInfoSync" bindingOperationName="WarehouseInfoSync" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="b7404773-bab7-4ef1-8fd2-171329c344b8" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://127.0.0.1:8001/Service/Interface</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">\r
   <soapenv:Header/>\r
   <soapenv:Body>\r
      <tem:WarehouseInfoSync>\r
         <!--Optional:-->\r
         <tem:inputJson><![CDATA[
{
  "isHaveMaterials": 0,
  "contactId": "contactId_449a0cc8a6ce",
  "contactName": "contactName_bf188b84076f",
  "gsId": "gsId_750723eb6d67",
  "gsName": "gsName_7845f26a8ac5",
  "bmId": "bmId_28fa2df24239",
  "bmName": "bmName_95b7fc9148eb",
  "teamName": "teamName_d027a87fc901",
  "warehouseStatus": "warehouseStatus_28da9981303e",
  "remark": "remark_7d41ed6d9bc5",
  "warehouseLevel": "warehouseLevel_c11c98349513",
  "describe": "describe_086c42ce5170",
  "isDeliveryAddress": "isDeliveryAddress_a0f5208df8d7",
  "isVirtualWarehouse": "isVirtualWarehouse_c50cb8c328cd",
  "physicalAddress": "physicalAddress_3c50e3fc3086",
  "stationPlace": "stationPlace_01644bb35381",
  "roomPlace": "roomPlace_9762ceac7aa1",
  "floor": "floor_349ed2a7acd9",
  "teamId": "teamId_f58be609b9c6",
  "warehouseArea": 0.00,
  "warehouseOutRoomArea": 0.00,
  "trainStation": "trainStation_ca38ed5f1537",
  "belongingPlace": "belongingPlace_b6038d8ab384",
  "warehouseRoomArea": 0.00,
  "phone": "phone_8df80c2b4a4c",
  "addressType": "addressType_1320819df76d",
  "warehouseChargeId": "warehouseChargeId_0a48743208df",
  "warehouseChargeName": "warehouseChargeName_6b01fb0cf153",
  "warehouseAddress": "warehouseAddress_efc8ff9c1053",
  "warehouseType": "warehouseType_cf501e50d4c9",
  "lineId": "lineId_b296e02971ab",
  "lineName": "lineName_f944780b11af",
  "mangeUserId": "mangeUserId_ffe573bb6cd6",
  "name": "name_094b87668d3b",
  "code": "code_0ee9b0e8715f",
  "warehouseShelfRelList": [
    {
      "shelfCode": "shelfCode_14f3fe973ee3",
      "warehouseId": "warehouseId_ad9c272e9cf1",
      "qrCode": "qrCode_1098cb1980a8",
      "shelfStatus": 0,
      "describe": "describe_9407c0728471",
      "shelfName": "shelfName_2ed2b6c84ae3",
      "shelfId": "shelfId_f9a7d56ac47e"
    }
  ],
  "createDate": "2025-07-16 17:25:02",
  "createUser": "createUser_3d6e4e5cf2ba",
  "createName": "createName_8a914a3b3565",
  "updateDate": "2025-07-16 17:25:02",
  "updateUser": "updateUser_63d12f2e4b68",
  "updateName": "updateName_3c8e4bec1c81",
  "id": "id_662d99894993",
  "status": 0,
  "billCode": "billCode_75740a68e96f"
}
]]]]>><![CDATA[</tem:inputJson>\r
      </tem:WarehouseInfoSync>\r
   </soapenv:Body>\r
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://tempuri.org/I_Interface/WarehouseInfoSync"/><con:wsrmConfig version="1.2"/></con:call></con:operation></con:interface><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/></con:soapui-project>