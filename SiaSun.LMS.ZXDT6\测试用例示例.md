# GoodsInfoSync 数组功能测试用例

## 测试用例 1：单个物料信息（向后兼容性测试）

### 输入JSON：
```json
{
    "goodsCode": "TEST001",
    "name": "测试物料001",
    "goodsStatus": 1,
    "unitName": "个",
    "goodsVersion": "V1.0",
    "brandVOs": [
        {
            "brandName": "测试品牌",
            "purchasesNum": "1",
            "inboundQuantity": 100
        }
    ]
}
```

### 期望输出：
```json
{
    "code": 0,
    "msg": "批量处理完成_总数[1]_成功[1]_失败[0]",
    "traceId": "生成的UUID"
}
```

## 测试用例 2：直接数组格式物料信息（正常情况）

### 输入JSON：
```json
[
    {
        "goodsCode": "BATCH001",
        "name": "批量测试物料001",
        "goodsStatus": 1,
        "unitName": "个",
        "goodsVersion": "V1.0",
        "brandVOs": [
            {
                "brandName": "品牌A",
                "purchasesNum": "1",
                "inboundQuantity": 50
            }
        ]
    },
    {
        "goodsCode": "BATCH002",
        "name": "批量测试物料002",
        "goodsStatus": 0,
        "unitName": "套",
        "goodsVersion": "V2.0",
        "brandVOs": [
            {
                "brandName": "品牌B",
                "purchasesNum": "2",
                "inboundQuantity": 75
            }
        ]
    }
]
```

### 期望输出：
```json
{
    "code": 0,
    "msg": "批量处理完成_总数[2]_成功[2]_失败[0]",
    "traceId": "生成的UUID"
}
```

## 测试用例 3：包含错误数据的直接数组处理

### 输入JSON：
```json
[
    {
        "goodsCode": "VALID001",
        "name": "有效物料001",
        "goodsStatus": 1,
        "unitName": "个",
        "goodsVersion": "V1.0",
        "brandVOs": [
            {
                "brandName": "有效品牌",
                "purchasesNum": "1",
                "inboundQuantity": 100
            }
        ]
    },
    {
        "goodsCode": "",
        "name": "无效物料001",
        "goodsStatus": 1,
        "unitName": "个",
        "goodsVersion": "V1.0"
    },
    {
        "goodsCode": "INVALID002",
        "name": "",
        "goodsStatus": 2,
        "unitName": "个",
        "goodsVersion": "V1.0"
    }
]
```

### 期望输出：
```json
{
    "code": 1,
    "msg": "批量处理部分成功_总数[3]_成功[1]_失败[2]_错误详情[物料[]:物料编码[goodsCode]不能为空;物料[INVALID002]:物料名称[name]不能为空]",
    "traceId": "生成的UUID"
}
```

## 测试用例 4：无效JSON格式

### 输入JSON：
```json
{
    "invalidFormat": "这不是有效的物料信息格式"
}
```

### 期望输出：
```json
{
    "code": 2,
    "msg": "调用入参解析错误，无法解析为有效的物料信息格式",
    "traceId": "生成的UUID"
}
```

## 测试用例 5：空的数组数据

### 输入JSON：
```json
[]
```

### 期望输出：
```json
{
    "code": 2,
    "msg": "调用入参解析错误，无法解析为有效的物料信息格式",
    "traceId": "生成的UUID"
}
```

## 测试步骤

1. **准备测试环境**
   - 确保数据库连接正常
   - 确保相关的数据表存在
   - 准备测试数据

2. **执行测试**
   - 逐个执行上述测试用例
   - 调用 `GoodsInfoSync.IntefaceMethod(inputJson)` 方法
   - 验证返回的JSON格式和内容

3. **验证数据库**
   - 检查 `GOODS_MAIN` 表中的数据是否正确插入/更新
   - 验证物料编码、名称、状态等字段
   - 确认品牌信息和单位信息的处理

4. **检查日志**
   - 验证日志记录是否完整
   - 确认成功和失败的操作都有相应的日志
   - 检查异常处理的日志记录

## 性能测试建议

1. **大批量数据测试**
   - 测试100个、500个、1000个物料的批量处理
   - 监控处理时间和内存使用
   - 验证数据库连接池的使用情况

2. **并发测试**
   - 模拟多个客户端同时调用接口
   - 验证数据一致性
   - 检查是否存在死锁或资源竞争

3. **错误恢复测试**
   - 模拟数据库连接中断
   - 测试部分数据处理失败的情况
   - 验证事务处理的正确性

## 注意事项

1. **数据备份**：在生产环境测试前，请确保数据库已备份
2. **权限检查**：确保测试账户有足够的数据库操作权限
3. **监控资源**：监控CPU、内存和数据库连接数的使用情况
4. **日志级别**：适当调整日志级别以便于问题排查
