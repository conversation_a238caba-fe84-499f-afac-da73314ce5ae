using System;
using System.Collections.Generic;
using Newtonsoft.Json;

namespace SiaSun.LMS.Test
{
    /// <summary>
    /// 测试 GoodsInfoSync 数组功能的示例类
    /// </summary>
    public class TestGoodsInfoSyncArray
    {
        /// <summary>
        /// 测试单个物料信息的JSON格式（向后兼容）
        /// </summary>
        public static string GetSingleGoodsJson()
        {
            var singleGoods = new
            {
                goodsCode = "TEST001",
                name = "测试物料001",
                goodsStatus = 1,
                unitName = "个",
                goodsVersion = "V1.0",
                brandVOs = new[]
                {
                    new
                    {
                        brandName = "测试品牌",
                        purchasesNum = "1",
                        inboundQuantity = 100
                    }
                }
            };

            return JsonConvert.SerializeObject(singleGoods, Formatting.Indented);
        }

        /// <summary>
        /// 测试直接数组格式物料信息的JSON格式
        /// </summary>
        public static string GetDirectArrayGoodsJson()
        {
            var directArrayGoods = new[]
            {
                new
                {
                    goodsCode = "BATCH001",
                    name = "批量测试物料001",
                    goodsStatus = 1,
                    unitName = "个",
                    goodsVersion = "V1.0",
                    brandVOs = new[]
                    {
                        new
                        {
                            brandName = "品牌A",
                            purchasesNum = "1",
                            inboundQuantity = 50
                        }
                    }
                },
                new
                {
                    goodsCode = "BATCH002",
                    name = "批量测试物料002",
                    goodsStatus = 0,
                    unitName = "套",
                    goodsVersion = "V2.0",
                    brandVOs = new[]
                    {
                        new
                        {
                            brandName = "品牌B",
                            purchasesNum = "2",
                            inboundQuantity = 75
                        }
                    }
                },
                new
                {
                    goodsCode = "BATCH003",
                    name = "批量测试物料003",
                    goodsStatus = 1,
                    unitName = "台",
                    goodsVersion = "V1.5",
                    brandVOs = new[]
                    {
                        new
                        {
                            brandName = "品牌C",
                            purchasesNum = "3",
                            inboundQuantity = 25
                        }
                    }
                }
            };

            return JsonConvert.SerializeObject(directArrayGoods, Formatting.Indented);
        }

        /// <summary>
        /// 测试包含错误数据的直接数组格式物料信息JSON格式
        /// </summary>
        public static string GetDirectArrayGoodsWithErrorsJson()
        {
            var directArrayGoods = new[]
            {
                new
                {
                    goodsCode = "VALID001",
                    name = "有效物料001",
                    goodsStatus = 1,
                    unitName = "个",
                    goodsVersion = "V1.0",
                    brandVOs = new[]
                    {
                        new
                        {
                            brandName = "有效品牌",
                            purchasesNum = "1",
                            inboundQuantity = 100
                        }
                    }
                },
                new
                {
                    goodsCode = "", // 错误：空的物料编码
                    name = "无效物料001",
                    goodsStatus = 1,
                    unitName = "个",
                    goodsVersion = "V1.0"
                },
                new
                {
                    goodsCode = "INVALID002",
                    name = "", // 错误：空的物料名称
                    goodsStatus = 2, // 错误：无效的状态值
                    unitName = "个",
                    goodsVersion = "V1.0"
                },
                new
                {
                    goodsCode = "VALID002",
                    name = "有效物料002",
                    goodsStatus = 0,
                    unitName = "套",
                    goodsVersion = "V2.0",
                    brandVOs = new[]
                    {
                        new
                        {
                            brandName = "另一个有效品牌",
                            purchasesNum = "1",
                            inboundQuantity = 50
                        }
                    }
                }
            };

            return JsonConvert.SerializeObject(directArrayGoods, Formatting.Indented);
        }

        /// <summary>
        /// 打印测试用的JSON示例
        /// </summary>
        public static void PrintTestExamples()
        {
            Console.WriteLine("=== 单个物料信息JSON示例（向后兼容） ===");
            Console.WriteLine(GetSingleGoodsJson());
            Console.WriteLine();

            Console.WriteLine("=== 直接数组格式物料信息JSON示例 ===");
            Console.WriteLine(GetDirectArrayGoodsJson());
            Console.WriteLine();

            Console.WriteLine("=== 包含错误的直接数组格式物料信息JSON示例 ===");
            Console.WriteLine(GetDirectArrayGoodsWithErrorsJson());
            Console.WriteLine();
        }
    }
}
