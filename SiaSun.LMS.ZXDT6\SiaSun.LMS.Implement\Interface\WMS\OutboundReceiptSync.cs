using SiaSun.LMS.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface.WMS
{
    /// <summary>
    /// 出库单接口 【SSWMS提供，iWMS调用】
    /// </summary>
    public class OutboundReceiptSync : InterfaceBase
    {
        class InputParam
        {
            public string deliverAddress { get; set; }
            public int isDeliver { get; set; }
            public int outboundType { get; set; }
            public int applyType { get; set; }
            public int isRedOut { get; set; }
            public string chargeUser { get; set; }
            public string chargeUserId { get; set; }
            public string deliverPhone { get; set; }
            public string deliverPeople { get; set; }
            public string deliverTime { get; set; }
            public int pickingType { get; set; }
            public string materialRoomWarehouseId { get; set; }
            public string materialRoomWarehouseName { get; set; }
            public int isPass { get; set; }
            public string outGoodsName { get; set; }
            public string operatorId { get; set; }
            public string operatorName { get; set; }
            public string operatTime { get; set; }
            public string describe { get; set; }
            public string closeDate { get; set; }
            public string applyUserName { get; set; }
            public string applyUserId { get; set; }
            public string applyDeptName { get; set; }
            public string applyDeptId { get; set; }
            public string workCode { get; set; }
            public string applyDate { get; set; }
            public string applyCode { get; set; }
            public List<OutGoodsInfoItem> outGoodsInfoList { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutGoodsInfoItem
        {
            public string remark { get; set; }
            public string applyUserName { get; set; }
            public string orderIndex { get; set; }
            public string applyGoodsId { get; set; }
            public string applyGoodsInfoId { get; set; }
            public int canUseNum { get; set; }
            public int redOutAllNum { get; set; }
            public string inventoryId { get; set; }
            public string materialRoomWarehouseId { get; set; }
            public string materialRoomWarehouseName { get; set; }
            public string outGoodsId { get; set; }
            public int isNeedWordOrder { get; set; }
            public string repairProcess { get; set; }
            public string major { get; set; }
            public string forWay { get; set; }
            public string forWayId { get; set; }
            public string projectInfo { get; set; }
            public string useForBuffer { get; set; }
            public string useFor { get; set; }
            public int goodsNum { get; set; }
            public string mgDeptName { get; set; }
            public string mgDeptId { get; set; }
            public string warehouseWay { get; set; }
            public string warehouseWayId { get; set; }
            public string shelfName { get; set; }
            public string shelfId { get; set; }
            public string warehouseName { get; set; }
            public string warehouseId { get; set; }
            public string goodsType { get; set; }
            public string goodsAttribute { get; set; }
            public string brand { get; set; }
            public string unitId { get; set; }
            public string unitName { get; set; }
            public string goodsVersion { get; set; }
            public string goodsName { get; set; }
            public string goodsCode { get; set; }
            public string goodsId { get; set; }
            public string createDate { get; set; }
            public string createUser { get; set; }
            public string createName { get; set; }
            public string updateDate { get; set; }
            public string updateUser { get; set; }
            public string updateName { get; set; }
            public string id { get; set; }
            public int status { get; set; }
            public string billCode { get; set; }
        }

        class OutputParam
        {
            public int code { get; set; }
            public string msg { get; set; }
            public string traceId { get; set; }
        }

        internal override string IntefaceMethod(string inputJson)
        {
            bool result = true;
            string message = string.Empty;
            string outputJson = string.Empty;
            OutputParam outputParam = null;
            string traceId = Guid.NewGuid().ToString();

            try
            {
                InputParam inputParam = Common.JsonHelper.Deserialize<InputParam>(inputJson);
                if (inputParam == null)
                {
                    result = false;
                    message = $"调用入参[{inputJson}]解析错误";
                    return FormatOutboundReceiptErrorMessage(message, traceId);
                }

                // 验证必填字段
                if (string.IsNullOrEmpty(inputParam.id))
                {
                    result = false;
                    message = "接口入参必填项存在空值：id";
                    return FormatOutboundReceiptErrorMessage(message, traceId);
                }

                // 验证出库明细列表
                if (inputParam.outGoodsInfoList == null || inputParam.outGoodsInfoList.Count == 0)
                {
                    result = false;
                    message = "出库明细列表不能为空";
                    return FormatOutboundReceiptErrorMessage(message, traceId);
                }

                // 验证出库明细项必填字段
                foreach (var item in inputParam.outGoodsInfoList)
                {
                    if (string.IsNullOrEmpty(item.goodsCode) || string.IsNullOrEmpty(item.id) || item.goodsNum <= 0)
                    {
                        result = false;
                        message = "出库明细项必填字段存在空值或无效值：id, goodsCode, goodsNum";
                        return FormatOutboundReceiptErrorMessage(message, traceId);
                    }
                }

                // 检查出库单是否已存在
                var existingReceipt = S_Base.sBase.pPLAN_MAIN.GetModelPlanCode(inputParam.applyCode);

                if (existingReceipt != null)
                {
                    result = false;
                    message = $"已存在出库单-{inputParam.applyCode}";
                    return FormatOutboundReceiptErrorMessage(message, traceId);
                }
                else
                {
                    PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN()
                    {
                        PLAN_CODE = inputParam.id,
                        PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanCommonOut.ToString(),
                        PLAN_CREATE_TIME = inputParam.createDate,
                        PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString(),
                        PLAN_CREATER = inputParam.createUser,
                        PLAN_FLAG = "1",
                    };

                    IList<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();
                    foreach (OutGoodsInfoItem item in inputParam.outGoodsInfoList)
                    {
                        PLAN_LIST mPLAN_LIST = new PLAN_LIST()
                        {
                            PLAN_LIST_CODE = item.id,
                            PLAN_LIST_QUANTITY = item.goodsNum,
                            GOODS_ID = S_Base.sBase.pGOODS_MAIN.GetModel(item.goodsCode)?.GOODS_ID ?? -1,
                            GOODS_PROPERTY1 = item.brand ?? string.Empty,
                        };
                        if (mPLAN_LIST.GOODS_ID == -1)
                        {
                            result = false;
                            message = $"不存在物料-{item.goodsCode}";
                            return FormatOutboundReceiptErrorMessage(message, traceId);
                        }
                        else
                        {
                            lsPLAN_LIST.Add(mPLAN_LIST);
                        }
                    }

                    result = S_Base.sBase.sPlan.PlanCreate(mPLAN_MAIN, lsPLAN_LIST, true, out int planID, out message);

                }
                // 模拟数据处理成功
                S_Base.sBase.Log.Info($"出库单同步成功 - 申请编码: {inputParam.applyCode}, 出库类型: {inputParam.outboundType}, 明细数量: {inputParam.outGoodsInfoList.Count}");

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("异常_信息[{0}]", ex.Message);
                S_Base.sBase.Log.Error($"出库单同步异常: {ex.Message}", ex);
            }
            finally
            {
                outputParam = new OutputParam()
                {
                    code = result ? 0 : 1,
                    msg = result ? "成功" : message,
                    traceId = traceId
                };
                outputJson = Common.JsonHelper.Serializer(outputParam);
            }

            return outputJson;
        }

        /// <summary>
        /// 格式化出库单错误消息
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <param name="traceId">跟踪ID</param>
        /// <returns>格式化的错误响应</returns>
        private string FormatOutboundReceiptErrorMessage(string message, string traceId)
        {
            OutputParam outputParam = new OutputParam()
            {
                code = 2, // 入参错误
                msg = message,
                traceId = traceId
            };
            return Common.JsonHelper.Serializer(outputParam);
        }
    }
}