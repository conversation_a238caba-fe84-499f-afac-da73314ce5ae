using System;
using System.Collections.Generic;
using System.Linq;
using SiaSun.LMS.Model;
using SiaSun.LMS.Implement.Interface.iWMS;

namespace SiaSun.LMS.Implement.Interface.MaterialSystem
{
    /// <summary>
    /// 出库任务物资系统接口调用策略
    /// </summary>
    public class OutboundInterfaceStrategy : IMaterialSystemInterfaceStrategy
    {
        public string StrategyName => "出库任务接口策略";

        /// <summary>
        /// 调用出库完成回调接口
        /// </summary>
        /// <param name="manageMain">任务主表信息</param>
        /// <param name="manageList">任务明细列表</param>
        /// <param name="message">返回消息</param>
        /// <returns>是否调用成功</returns>
        public bool CallInterface(MANAGE_MAIN manageMain, IList<MANAGE_LIST> manageList, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                // 参数校验
                if (manageMain == null)
                {
                    result = false;
                    message = "任务主表信息不能为空";
                    return result;
                }

                if (manageList == null || manageList.Count == 0)
                {
                    result = false;
                    message = "任务明细列表不能为空";
                    return result;
                }

                // 获取起始货位信息（出库是从起始货位出货）
                var startCell = S_Base.sBase.pWH_CELL.GetModel(manageMain.START_CELL_ID);
                if (startCell == null)
                {
                    result = false;
                    message = $"未找到起始货位信息_货位ID[{manageMain.START_CELL_ID}]";
                    return result;
                }

                // 获取仓库信息
                var area = S_Base.sBase.pWH_AREA.GetModel(startCell.AREA_ID);
                if (area == null)
                {
                    result = false;
                    message = $"未找到货位所属区域信息_区域ID[{startCell.AREA_ID}]";
                    return result;
                }

                // 构建出库完成明细列表
                var outboundItems = new List<OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem>();

                foreach (var manageItem in manageList)
                {
                    // 获取计划明细信息（用于获取原始ID）
                    var planList = S_Base.sBase.pPLAN_LIST.GetModel(manageItem.PLAN_LIST_ID);
                    
                    var outboundItem = new OutboundReceiptCompleteCallback.OutboundReceiptCompleteItem
                    {
                        goodsNum = (int)manageItem.MANAGE_LIST_QUANTITY,
                        warehouseCode = "1",
                        shelfCode = startCell.CELL_CODE.Contains("1200") ? "ZCQ01" : startCell.CELL_CODE,
                        oId = planList?.PLAN_LIST_CODE ?? manageItem.MANAGE_LIST_ID.ToString(), // 原始明细ID
                        lId = manageItem.MANAGE_LIST_ID.ToString(), // 立体仓系统明细ID
                        outboundType = GetOutboundType(manageMain.MANAGE_TYPE_CODE) // 出库类型
                    };

                    outboundItems.Add(outboundItem);
                }

                // 调用出库完成回调接口
                var callback = new OutboundReceiptCompleteCallback();
                result = callback.IntefaceMethod(outboundItems, out message);

                if (result)
                {
                    S_Base.sBase.Log.Info($"出库完成接口调用成功_任务ID[{manageMain.MANAGE_ID}]_托盘条码[{manageMain.STOCK_BARCODE}]_明细数量[{outboundItems.Count}]");
                }
                else
                {
                    S_Base.sBase.Log.Error($"出库完成接口调用失败_任务ID[{manageMain.MANAGE_ID}]_托盘条码[{manageMain.STOCK_BARCODE}]_错误信息[{message}]");
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = $"出库完成接口调用异常：{ex.Message}";
                S_Base.sBase.Log.Error($"出库完成接口调用异常_任务ID[{manageMain?.MANAGE_ID}]_异常信息[{ex.Message}]", ex);
            }

            return result;
        }

        /// <summary>
        /// 根据任务类型获取出库类型
        /// </summary>
        /// <param name="manageTypeCode">任务类型编码</param>
        /// <returns>出库类型：63=出库单，72=入库红冲单，74=借用</returns>
        private int GetOutboundType(string manageTypeCode)
        {
            switch (manageTypeCode)
            {
                case "ManageOut":
                    return 63; // 出库单
                case "ManageAdjustOut":
                    return 72; // 入库红冲单（盘亏出库）
                case "ManageDown":
                    return 74; // 借用（下架可视为借用）
                default:
                    return 63; // 默认为出库单
            }
        }
    }
}
